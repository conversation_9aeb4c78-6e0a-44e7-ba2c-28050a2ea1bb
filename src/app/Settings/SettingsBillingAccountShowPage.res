open Intl

module BillingAccountRequest = {
  let decodeResult = json => {
    let billingAccountDict = json->Json.decodeDict
    let shippingAddressDict =
      billingAccountDict
      ->Json.flatDecodeDictFieldDict("shipping")
      ->Json.flatDecodeDictFieldDict("address")
    let billingAddressDict = billingAccountDict->Json.flatDecodeDictFieldDict("address")
    let paymentMethodDict = billingAccountDict->Json.flatDecodeDictFieldDict("defaultPaymentMethod")

    let ibanLast4 =
      paymentMethodDict
      ->Json.flatDecodeDictFieldDict("iban")
      ->Json.flatDecodeDictFieldString("last4")

    let decodeAddress = json =>
      switch (
        json->Json.flatDecodeDictFieldString("address"),
        json->Json.flatDecodeDictFieldString("postalCode"),
        json->Json.flatDecodeDictFieldString("city"),
        json->Json.flatDecodeDictFieldString("country"),
      ) {
      | (Some(address), Some(postalCode), Some(city), Some(country)) =>
        Some({CorporateEntity.Address.address, postalCode, city, country})
      | _ => None
      }

    let corporateName = billingAccountDict->Json.flatDecodeDictFieldString("corporateName")
    let shopName = billingAccountDict->Json.flatDecodeDictFieldString("shopName")
    let email = billingAccountDict->Json.flatDecodeDictFieldString("email")
    let phone = billingAccountDict->Json.flatDecodeDictFieldString("phone")
    let billingAddress = decodeAddress(billingAddressDict)
    let shippingAddress = decodeAddress(shippingAddressDict)
    let ibanLast4 = ibanLast4
    let vatNumber = billingAccountDict->Json.flatDecodeDictFieldString("vatNumber")

    Some({
      BillingAccount.corporateName,
      shopName,
      email,
      phone,
      billingAddress,
      shippingAddress,
      vatNumber,
      iban: switch ibanLast4 {
      | Some(ibanLast4) => Some({last4: ibanLast4})
      | None => None
      },
    })
  }

  let endpoint = Env.gatewayUrl() ++ "/customer-billing-account/"

  let make = (~shopId) => Request.make(endpoint ++ shopId, ~method=#GET)->Future.mapOk(decodeResult)
}

module BillingStatusRequest = {
  let decodeBillingIssues = failure => {
    let {Request.data: data, kind} = failure
    let dueDate =
      data
      ->Option.flatMap(Json.decodeDict)
      ->Json.flatDecodeDictFieldFloat("dueDate")
      ->Option.map(Js.Date.fromFloat)
    switch kind->BillingAccount.BillingIssue.fromString(dueDate) {
    | Ok(issue) => Some(issue)
    | _ => None
    }
  }

  let make = (~shopId) =>
    Request.make(
      Env.gatewayUrl() ++ "/billing-accounts/" ++ shopId ++ "/audit",
      ~method=#GET,
    )->Future.map(result =>
      switch result {
      | Ok(issues) =>
        Ok(
          issues
          ->Json.decodeArray
          ->Option.flatMap(failures =>
            failures
            ->Request.decodeInvalidRequestFailures
            ->Option.map(
              failures =>
                failures
                ->Array.keepMap(decodeBillingIssues)
                ->BillingAccount.BillingIssue.mostImportantIssue,
            )
          )
          ->Option.getWithDefault(None),
        )

      | Error(error) => Error(error)
      }
    )
}

module SepaMandateRequest = {
  let decodeResult = json =>
    switch (
      json->Json.decodeDict->Json.flatDecodeDictFieldString("status"),
      json->Json.decodeDict->Json.flatDecodeDictField("acceptedAt", Json.decodeNumber),
    ) {
    | (Some(status), Some(acceptedAt)) =>
      switch status {
      | "active" => Some({CorporateEntity.SepaMandate.status: Active, acceptedAt: Some(acceptedAt)})
      | "inactive" => Some({status: Inactive, acceptedAt: Some(acceptedAt)})
      | "pending" => Some({status: Pending, acceptedAt: Some(acceptedAt)})
      | _ => None
      }
    | _ => None
    }

  let endpoint = Env.gatewayUrl() ++ "/billing-account/sepa-mandate/"

  let make = (~shopId) => Request.make(endpoint ++ shopId, ~method=#GET)->Future.mapOk(decodeResult)
}

module UpdatePaymentMethodRequest = {
  let encodeBody = (~shopId, ~ibanNumber, ~ipAddress, ~userAgent, ~acceptedAt) =>
    Js.Dict.fromArray([
      ("shopId", shopId->Json.encodeString),
      ("iban", ibanNumber->Json.encodeString),
      ("ipAddress", ipAddress->Json.encodeString),
      ("userAgent", userAgent->Json.encodeString),
      ("acceptedAt", acceptedAt->Json.encodeNumber),
    ])->Json.encodeDict

  let endpoint = Env.gatewayUrl() ++ "/billing-account/default-payment-method"

  type failureKind = WrongSepaMandateAcceptanceDetails | InvalidIban | Unknown

  let decodeInvalidRequestFailure = error =>
    switch error {
    | {Request.kind: "WrongSepaMandateAcceptanceDetails"} => WrongSepaMandateAcceptanceDetails
    | {kind: "InvalidIban"} => InvalidIban
    | _ => Unknown
    }

  let make = (~shopId, ~ibanNumber, ~ipAddress, ~userAgent, ~acceptedAt) =>
    Request.make(
      endpoint,
      ~method=#PATCH,
      ~bodyJson=encodeBody(~shopId, ~ibanNumber, ~ipAddress, ~userAgent, ~acceptedAt),
    )
    ->Future.mapOk(_ => ())
    ->Future.mapError(error =>
      switch error {
      | InvalidRequestFailures(invalidRequestFailures) =>
        invalidRequestFailures[0]->Option.map(decodeInvalidRequestFailure)
      | _ => Some(Unknown)
      }
    )
}

module SubscriptionsRequest = {
  open BillingAccount

  let decodeCustomerPlans = json => {
    let dict = json->Json.decodeDict
    switch (
      dict->Json.flatDecodeDictFieldString("kind"),
      dict->Json.flatDecodeDictField("upcomingInvoiceDate", Json.decodeNumber),
      dict->Json.flatDecodeDictField("quantity", Json.decodeNumber),
    ) {
    | (Some(kind), upcomingInvoiceDate, Some(quantity)) => {
        let upcomingInvoiceDate = switch upcomingInvoiceDate {
        | Some(date) => Some(Js.Date.fromFloat(date))
        | None => None
        }
        switch (kind->BillingPlanKind.fromString, upcomingInvoiceDate, quantity) {
        | (Ok(kind), upcomingInvoiceDate, quantity) =>
          Some({
            BillingAccount.kind,
            upcomingInvoiceDate,
            quantity: quantity->Int.fromFloat,
          })
        | _ => None
        }
      }

    | _ => None
    }
  }

  let decodeCustomerOptions = json => {
    let dict = json->Json.decodeDict
    switch (
      dict->Json.flatDecodeDictFieldString("name"),
      dict->Json.flatDecodeDictField("upcomingInvoiceDate", Json.decodeNumber),
      dict->Json.flatDecodeDictField("quantity", Json.decodeNumber),
    ) {
    | (Some(name), upcomingInvoiceDate, Some(quantity)) => {
        let upcomingInvoiceDate = switch upcomingInvoiceDate {
        | Some(date) => Some(Js.Date.fromFloat(date))
        | None => None
        }
        Some({
          BillingAccount.name,
          upcomingInvoiceDate,
          quantity: quantity->Int.fromFloat,
        })
      }

    | _ => None
    }
  }

  let decodeResult = json =>
    switch (
      json->Json.decodeDict->Json.flatDecodeDictFieldArray("plans"),
      json->Json.decodeDict->Json.flatDecodeDictFieldArray("options"),
    ) {
    | (Some(plans), Some(options)) =>
      let subscriptions = {
        BillingAccount.plans: plans->Array.keepMap(decodeCustomerPlans),
        options: options->Array.keepMap(decodeCustomerOptions),
      }
      Some(subscriptions)
    | _ => None
    }

  let endpoint = Env.gatewayUrl() ++ "/customer-subscriptions/"

  let make = (~shopId) => Request.make(endpoint ++ shopId, ~method=#GET)->Future.mapOk(decodeResult)
}

module ConfirmSepaMandateRequest = {
  let encodeBody = (~shopId, ~ipAddress, ~userAgent, ~acceptedAt) =>
    Js.Dict.fromArray([
      ("shopId", shopId->Json.encodeString),
      ("ipAddress", ipAddress->Json.encodeString),
      ("userAgent", userAgent->Json.encodeString),
      ("acceptedAt", acceptedAt->Json.encodeNumber),
    ])->Json.encodeDict

  let endpoint = Env.gatewayUrl() ++ "/billing-account/sepa-mandate"

  type failureKind = WrongSepaMandateAcceptanceDetails | IpAddressFailure | Unknown

  let decodeInvalidRequestFailure = error =>
    switch error {
    | {Request.kind: "WrongSepaMandateAcceptanceDetails"} => WrongSepaMandateAcceptanceDetails
    | _ => Unknown
    }

  let make = (~shopId, ~ipAddress, ~userAgent, ~acceptedAt) =>
    Request.make(
      endpoint,
      ~method=#PATCH,
      ~bodyJson=encodeBody(~shopId, ~ipAddress, ~userAgent, ~acceptedAt),
    )
    ->Future.mapOk(_ => ())
    ->Future.mapError(error =>
      switch error {
      | InvalidRequestFailures(invalidRequestFailures) =>
        invalidRequestFailures->Array.get(0)->Option.map(decodeInvalidRequestFailure)
      | _ => Some(Unknown)
      }
    )
}

module SubscriptionsCard = {
  @react.component
  let make = (~subscriptions) => {
    <Card title={t("Subscription")}>
      <Stack space=#medium>
        {switch subscriptions {
        | Some(subscriptions) if subscriptions.BillingAccount.plans->Array.length > 0 =>
          <Box spaceTop=#xsmall spaceBottom=#none>
            {subscriptions.plans
            ->Array.map(plan =>
              <Inline space=#xsmall key={plan.kind->BillingAccount.BillingPlanKind.toString}>
                <TextStyle variation={#primary} weight={#medium}>
                  {t("Subscription")->React.string}
                </TextStyle>
                <TextStyle variation={#primary} weight={#strong}>
                  {t(plan.kind->BillingAccount.BillingPlanKind.toString)->React.string}
                  {if plan.quantity > 1 {
                    (" (x" ++ plan.quantity->Int.toString ++ ")")->React.string
                  } else {
                    React.null
                  }}
                </TextStyle>
                {switch plan.upcomingInvoiceDate {
                | Some(upcomingInvoiceDate) =>
                  let formattedUpcomingInvoiceDate =
                    t("Next charge on") ++ " " ++ upcomingInvoiceDate->Intl.dateTimeFormat ++ "."
                  <Offset top={1.} height=18.>
                    <TooltipIcon variation=#info>
                      <Tooltip.Span text=formattedUpcomingInvoiceDate />
                    </TooltipIcon>
                  </Offset>
                | None => React.null
                }}
              </Inline>
            )
            ->React.array}
            {if subscriptions.options->Array.length > 0 {
              let triggerToggleView =
                <Box spaceY=#none>
                  <TextStyle weight=#strong> {t("Options")->React.string} </TextStyle>
                </Box>

              <Box spaceTop=#small>
                <Accordion triggerShowView=triggerToggleView triggerHideView=triggerToggleView>
                  <Box spaceLeft=#xlarge spaceBottom=#small>
                    <Stack space=#medium>
                      {subscriptions.options
                      ->Array.map(option =>
                        <Box spaceLeft=#xsmall spaceTop=#none>
                          <TextStyle opacity=0.6>
                            {t(option.name)->React.string}
                            {if option.quantity > 1 {
                              (" (x" ++ option.quantity->Int.toString ++ ")")->React.string
                            } else {
                              React.null
                            }}
                          </TextStyle>
                        </Box>
                      )
                      ->React.array}
                    </Stack>
                  </Box>
                </Accordion>
              </Box>
            } else {
              React.null
            }}
          </Box>
        | _ =>
          <Banner textStatus=Warning(t("Unable to display information about your subscription.")) />
        }}
        <TextStyle>
          {(t("Would you like to change your subscription?") ++ " ")->React.string}
          // TODO - Waiting for subscription page to be created (CSM)
          // <TextLink
          //   highlighted=true
          //   openNewTab=true
          //   text={t("Discover our subscriptions.")}
          // />
        </TextStyle>
        <Button size=#medium variation=#neutral onPress={_ => HelpCenter.showMessages()}>
          {t("Contact us")->React.string}
        </Button>
        <Box spaceTop=#xsmall>
          <TextAction
            highlighted=true
            text={t("Review the terms and conditions of sale.")}
            onPress={() => HelpCenter.showArticle(HelpCenter.termsAndConditions)}
          />
        </Box>
      </Stack>
    </Card>
  }
}

module EditPaymentMethodFormLenses = %lenses(type state = {ibanNumber: string})

module EditPaymentMethodForm = Form.Make(EditPaymentMethodFormLenses)

module EditPaymentMethodFormModal = {
  let schema = [
    EditPaymentMethodForm.Schema.CustomString(
      IbanNumber,
      (value, _) => {
        if value->CorporateEntity.Iban.validate {
          Ok()
        } else {
          Error(t("The IBAN number is not valid."))
        }
      },
    ),
  ]

  @react.component
  let make = (
    ~opened,
    ~onRequestCloseModal,
    ~title,
    ~activeShopId,
    ~alertBannerContent,
    ~updatePaymentMethodRequest,
    ~reloadBillingAccount,
    ~ipAddressRequest,
    ~reloadAlertBar,
    ~info,
  ) => {
    let (error, setError) = React.useState(_ => None)
    let captureEvent = SessionTracker.useCaptureEvent()

    let onSubmit = (_, {EditPaymentMethodFormLenses.ibanNumber: ibanNumber}) =>
      ipAddressRequest()
      ->Future.flatMap(ipRequestResult =>
        switch ipRequestResult {
        | Ok(ipAddress) =>
          updatePaymentMethodRequest(
            ~shopId=activeShopId,
            ~ibanNumber,
            ~ipAddress,
            ~userAgent=UserAgent.get(),
            ~acceptedAt=Js.Date.now(),
          )->Future.mapError(error => #UpdatePayementMethodFailure(error))

        | Error() => Future.value(Error(#IpAddressFailure))
        }
      )
      ->Future.flatMapOk(_ =>
        Future.all2((reloadAlertBar(), reloadBillingAccount()))->Future.map(_ => Ok())
      )
      ->Future.map(result =>
        switch result {
        | Ok() => Ok(None)
        | Error(error) =>
          if error === #IpAddressFailure {
            BugTracker.reportErrorMessage("Fetching IP on billing settings failed")
          }
          Error(
            switch error {
            | #UpdatePayementMethodFailure(Some(UpdatePaymentMethodRequest.InvalidIban)) =>
              t("The IBAN number is not valid.")
            | #UpdatePayementMethodFailure(Some(WrongSepaMandateAcceptanceDetails)) =>
              let message = "Your internet connection has been interrupted, please reload the page before submitting the form again."
              t(message)
            | #IpAddressFailure | #UpdatePayementMethodFailure(None | Some(Unknown)) =>
              t("An unexpected error occured. Please try again or contact the support.")
            },
          )
        }
      )
      ->Future.tapOk(_ => captureEvent(#validate_sepa_mandate))

    let onSubmitSuccess = _ => {
      onRequestCloseModal()
      setError(_ => None)
    }

    let onSubmitFailure = message => setError(_ => Some(message))

    let formPropState = EditPaymentMethodForm.useFormPropState({
      initialValues: {ibanNumber: ""},
      schema,
      resetValuesAfterSubmission: true,
      onSubmitFailure,
      onSubmitSuccess,
    })

    let onRequestClose = _ => onRequestCloseModal()

    <Modal title hideFooter=true opened onRequestClose>
      {switch alertBannerContent {
      | Some(textStatus) => <Banner textStatus />
      | None => React.null
      }}
      <EditPaymentMethodForm.FormProvider propState=formPropState>
        {switch error {
        | Some(message) =>
          <Box spaceTop=#xsmall spaceX=#xlarge>
            <Banner textStatus=Danger(message) onRequestClose={_ => setError(_ => None)} />
          </Box>
        | _ => React.null
        }}
        <Box spaceTop=#xlarge spaceX=#xlarge>
          {switch info {
          | Some(info) =>
            <Box spaceBottom=#medium>
              <TextStyle opacity=0.5> {t(info)->React.string} </TextStyle>
            </Box>
          | None => React.null
          }}
          <Stack space=#small>
            <EditPaymentMethodForm.InputText
              field=IbanNumber
              label={t("IBAN number")}
              placeholder={t("FR12 1234 5678 1000 0000 0000 123")}
            />
            <Box spaceTop=#medium>
              <Banner
                textStatus=Info(
                  t(
                    "By providing your IBAN and clicking 'Confirm' from this window, you authorize Wino Technologies and Stripe, our payment service provider, to send instructions to your bank to debit your account, and your bank to debit your account in accordance with these instructions. You are entitled to a refund from your bank according to the terms and conditions of the agreement you have with them. The refund must be requested within 8 weeks from the date your account was debited.",
                  ),
                )
              />
            </Box>
          </Stack>
          <Box spaceY=#xlarge>
            <Inline align=#end space=#xmedium>
              <Button size=#large variation=#neutral onPress={_ => onRequestClose()}>
                {t("Cancel")->React.string}
              </Button>
              <EditPaymentMethodForm.SubmitButton text={t("Confirm")} variation=#success onSubmit />
            </Inline>
          </Box>
        </Box>
      </EditPaymentMethodForm.FormProvider>
    </Modal>
  }
}

module ConfirmSepaMandateModal = {
  @react.component
  let make = (
    ~opened,
    ~onRequestCloseModal,
    ~alertBannerContent,
    ~activeShopId,
    ~confirmSepaMandateRequest,
    ~ipAddressRequest,
    ~reloadBillingAccount,
    ~reloadAlertBar,
  ) => {
    let (error, setError) = React.useState(_ => None)
    let (loading, setLoading) = React.useState(_ => false)

    let onPress = _ => {
      setLoading(_ => true)

      ipAddressRequest()
      ->Future.flatMap(ipRequestResult =>
        switch ipRequestResult {
        | Ok(ipAddress) =>
          confirmSepaMandateRequest(
            ~shopId=activeShopId,
            ~ipAddress,
            ~userAgent=UserAgent.get(),
            ~acceptedAt=Js.Date.now(),
          )->Future.mapError(_ => #ConfirmSepaMandateRequestFailure)
        | Error() => Future.value(Error(#IpAddressFailure))
        }
      )
      ->Future.flatMapOk(_ =>
        Future.all2((reloadAlertBar(), reloadBillingAccount()))->Future.map(_ => Ok())
      )
      ->Future.get(result => {
        setLoading(_ => false)
        switch result {
        | Ok() =>
          setError(_ => None)
          onRequestCloseModal()
        | Error(error) =>
          if error === #ConfirmSepaMandateRequestFailure {
            BugTracker.reportErrorMessage("Fetching IP on billing settings failed")
          }
          let message = switch error {
          | #IpAddressFailure => "Your internet connection has been interrupted, please reload the page before submitting the form again."
          | #ConfirmSepaMandateRequestFailure => "An unexpected error occured. Please try again or contact the support."
          }
          setError(_ => Some(t(message)))
        }
      })
    }

    let onRequestClose = _ => onRequestCloseModal()

    <Modal
      title={t("Confirm Your SEPA Direct Debit Authorization")}
      hideFooter=true
      opened
      onRequestClose>
      {switch alertBannerContent {
      | Some(textStatus) => <Banner textStatus />
      | None => React.null
      }}
      {switch error {
      | Some(message) =>
        <Box spaceTop=#xsmall spaceX=#xlarge>
          <Banner textStatus=Danger(message) onRequestClose={_ => setError(_ => None)} />
        </Box>
      | _ => React.null
      }}
      <Box spaceTop=#xlarge spaceX=#xlarge>
        <Banner
          textStatus=Info(
            t(
              "By providing your IBAN and clicking 'Confirm' from this window, you authorize Wino Technologies and Stripe, our payment service provider, to send instructions to your bank to debit your account, and your bank to debit your account in accordance with these instructions. You are entitled to a refund from your bank according to the terms and conditions of the agreement you have with them. The refund must be requested within 8 weeks from the date your account was debited.",
            ),
          )
        />
        <Box spaceY=#xlarge>
          <Inline align=#end space=#xmedium>
            <Button
              size=#large
              variation=#neutral
              onPress={_ => onRequestClose()}
              disabled={loading === true}>
              {t("Cancel")->React.string}
            </Button>
            <Button size=#large variation=#success onPress loading>
              {t("Confirm")->React.string}
            </Button>
          </Inline>
        </Box>
      </Box>
    </Modal>
  }
}

module ErrorBanner = {
  @react.component
  let make = () =>
    <Banner
      textStatus=Warning(
        t(t("Some subscription and billing settings cannot be displayed at the moment.")),
      )
    />
}

module PaymentMethodCard = {
  @react.component
  let make = (
    ~confirmSepaModalOpened,
    ~editPaymentMethodModalOpened,
    ~onRequestCloseModal,
    ~handleOpenConfirmMandateModal,
    ~handleOpenEditPaymentMethodModal,
    ~billingAccount,
    ~billingStatus,
    ~sepaMandateRequest,
    ~activeShopId,
    ~shopName,
    ~updatePaymentMethodRequest,
    ~confirmSepaMandateRequest,
    ~ipAddressRequest,
    ~reloadBillingAccount,
    ~reloadAlertBar,
  ) => {
    let scope = Auth.useScope()

    let organisationAccount = switch scope {
    | Organisation(_) => true
    | Single(_) => false
    }
    let (successNotification, setSuccessNotification) = React.useState(_ => None)

    let (iconAction, textAction, info) = switch (billingAccount, billingStatus) {
    | (_, Some(BillingAccount.BillingIssue.MissingPaymentMethod))
    | (Some({BillingAccount.iban: None}), None) => (#plus_light, "Add a payment method", None)
    | (_, _) => (
        #edit_light,
        "Change payment method",
        Some("When confirming a new payment method, your current payment method will be removed."),
      )
    }
    let action: Card.Action.t = {
      icon: iconAction,
      title: t(textAction),
      handler: Callback(handleOpenEditPaymentMethodModal),
    }

    let alertBannerContentUpdatePayment = {
      switch (billingStatus, organisationAccount) {
      | (Some(MissingPaymentMethod), true) =>
        Some(
          Banner.Danger(
            template(
              t("Valid payment method is required for the shop {{shopName}}."),
              ~values={
                "shopName": shopName,
              },
              (),
            ),
          ),
        )
      | (Some(MissingPaymentMethod), false) =>
        Some(Banner.Danger(t("Valid payment method is required.")))
      | (_, _) => None
      }
    }

    let (mandate, setMandate) = React.useState(_ => AsyncResult.notAsked())

    let editPaymentMethodModalTitle = switch billingAccount {
    | Some({BillingAccount.iban: Some(_)}) => t("Edit Payment Method")
    | _ => t("Add a payment method")
    }

    React.useEffect1(() => {
      switch billingStatus {
      | None =>
        let request = sepaMandateRequest(~shopId=activeShopId)
        setMandate(_ => Loading)

        request->Future.get(results => {
          switch results {
          | Ok(mandate) => setMandate(_ => Done(Ok(mandate)))
          | _ => setMandate(_ => Done(Error()))
          }
        })
        Some(() => request->Future.cancel)
      | Some(_) => None
      }
    }, [billingStatus])

    <>
      <Card title={t("Payment method")} action>
        <Stack space=#medium>
          {switch (billingAccount, billingStatus) {
          | (Some({BillingAccount.iban: Some({last4})}), _) =>
            <>
              <TextStyle>
                {(t("SEPA direct debit") ++ " • • • • " ++ last4)->React.string}
              </TextStyle>
              {switch mandate {
              | Done(Ok(Some({
                  CorporateEntity.SepaMandate.status: Active,
                  acceptedAt: Some(acceptedAt),
                }))) =>
                <TextStyle>
                  {(t("Accepted mandate on") ++
                  " " ++
                  acceptedAt->Js.Date.fromFloat->Intl.dateTimeFormat(~dateStyle=#short))
                    ->React.string}
                </TextStyle>
              | Done(Error(_)) => <ErrorBanner />
              | Loading => <Spinner />
              | _ => React.null
              }}
            </>
          | (Some({BillingAccount.iban: None}), None) =>
            <Banner textStatus=Warning(t("Attention, no valid payment method is registered.")) />
          | (_, _) => React.null
          }}
          {switch (billingStatus, successNotification) {
          | (Some(MissingPaymentMethod), None) =>
            <Banner textStatus=Danger(t("Attention, no valid payment method is registered.")) />
          | (Some(InvalidBillingMandate), None) =>
            <Stack space=#medium>
              <Banner
                textStatus=Warning(t("Please confirm your SEPA direct debit authorization."))
              />
              <Button variation=#neutral onPress={_ => handleOpenConfirmMandateModal()}>
                {t("Confirm")->React.string}
              </Button>
            </Stack>
          | _ => React.null
          }}
          {switch successNotification {
          | Some(successNotification) =>
            <Box>
              <Banner
                textStatus=successNotification
                onRequestClose={_ => setSuccessNotification(_ => None)}
              />
            </Box>
          | None => React.null
          }}
        </Stack>
      </Card>
      <EditPaymentMethodFormModal
        opened=editPaymentMethodModalOpened
        onRequestCloseModal
        title=editPaymentMethodModalTitle
        activeShopId
        alertBannerContent=alertBannerContentUpdatePayment
        updatePaymentMethodRequest
        reloadBillingAccount
        ipAddressRequest
        reloadAlertBar
        info
      />
      <ConfirmSepaMandateModal
        opened=confirmSepaModalOpened
        onRequestCloseModal
        alertBannerContent=None
        activeShopId
        confirmSepaMandateRequest
        ipAddressRequest
        reloadBillingAccount
        reloadAlertBar
      />
    </>
  }
}

module AddressBox = {
  @react.component
  let make = (~value, ~title) =>
    <InfoBlock title>
      {switch value {
      | Some({CorporateEntity.Address.address: address, postalCode, city, country}) =>
        let country = switch country->CountryCode.fromString {
        | Ok(countrycode) =>
          <TextStyle> {t(countrycode->CountryCode.toMediumCountryString)->React.string} </TextStyle>
        | _ =>
          <TextStyle variation={#subdued}> {t("Country not specified")->React.string} </TextStyle>
        }
        <>
          <TextStyle> {address->React.string} </TextStyle>
          <TextStyle> {(postalCode ++ " " ++ city)->React.string} </TextStyle>
          {country}
        </>
      | None =>
        <TextStyle variation={#subdued}> {t("Address not provided")->React.string} </TextStyle>
      }}
    </InfoBlock>
}

module BillingInformationCard = {
  @react.component
  let make = (~activeShopId, ~billingAccount: option<BillingAccount.info>) => {
    switch billingAccount {
    | Some(billingAccount) => {
        let {
          BillingAccount.corporateName: corporateName,
          shopName,
          email,
          phone,
          billingAddress,
          shippingAddress,
          vatNumber,
        } = billingAccount

        <Card
          title={t("Billing information")}
          action={{
            icon: #edit_light,
            title: t("Edit billing information"),
            handler: OpenLink(
              RouteWithQueryString(
                SettingsRoutes.billingAccountEditRoute,
                SettingsRoutes.encodeEditBillingAccountQueryString(
                  ~activeShopId,
                  ~corporateName=corporateName->Option.getWithDefault(""),
                  ~shopName=shopName->Option.getWithDefault(""),
                  ~email=email->Option.getWithDefault(""),
                  ~phone=phone->Option.getWithDefault(""),
                  ~billingAddress,
                  ~shippingAddress,
                  ~vatNumber,
                ),
              ),
            ),
          }}>
          <Stack space=#large>
            <Stack>
              {switch corporateName {
              | Some(corporateName) => <TextStyle> {corporateName->React.string} </TextStyle>
              | None =>
                <TextStyle variation={#subdued}>
                  {t("Corporate name not provided")->React.string}
                </TextStyle>
              }}
              {switch shopName {
              | Some(shopName) => <TextStyle> {shopName->React.string} </TextStyle>
              | None => React.null
              }}
              {switch email {
              | Some(email) => <TextStyle> {email->React.string} </TextStyle>
              | None =>
                <TextStyle variation={#subdued}>
                  {t("Email not provided")->React.string}
                </TextStyle>
              }}
              {switch phone {
              | Some(phone) => <TextStyle> {phone->React.string} </TextStyle>
              | None =>
                <TextStyle variation={#subdued}>
                  {t("Phone number not provided")->React.string}
                </TextStyle>
              }}
            </Stack>
            {switch vatNumber {
            | Some(vatNumber) => <TextStyle> {vatNumber->React.string} </TextStyle>
            | None =>
              <TextStyle variation={#subdued}>
                {t("VAT number not provided")->React.string}
              </TextStyle>
            }}
            <AddressBox title={t("Billing address")} value=billingAddress />
            <AddressBox title={t("Shipping address")} value=shippingAddress />
            {if (
              [corporateName, email, vatNumber, phone]->Array.some(Option.isNone) ||
                [billingAddress, shippingAddress]->Array.some(Option.isNone)
            ) {
              <Banner
                textStatus=Warning(t("Attention, important information needs to be completed."))
              />
            } else {
              React.null
            }}
          </Stack>
        </Card>
      }

    | _ => React.null
    }
  }
}

module WarningNotificationBanner = {
  @react.component
  let make = (~billingAccount) => {
    switch billingAccount {
    | Some({BillingAccount.vatNumber: None})
    | Some({phone: None})
    | Some({billingAddress: None})
    | Some({shippingAddress: None})
    | Some({email: None})
    | Some({corporateName: None}) =>
      let text = "Some billing information are missing. While this is not required to use the solution, providing it is necessary to ensure legal compliance."
      <Box spaceTop=#large>
        <Banner textStatus=Warning(t(text)) />
      </Box>
    | _ => React.null
    }
  }
}

module BillingInvoicesCard = {
  @react.component
  let make = (~shopId, ~invoicesRequest) =>
    <Card grow=true title={t("Invoice history")} variation={#table}>
      <BillingAccountInvoicesTable shopId preview=true invoicesRequest />
    </Card>
}

let billingAccountRequest = BillingAccountRequest.make
let billingStatusRequest = BillingStatusRequest.make
let sepaMandateRequest = SepaMandateRequest.make
let updatePaymentMethodRequest = UpdatePaymentMethodRequest.make
let subscriptionsRequest = SubscriptionsRequest.make
let confirmSepaMandateRequest = ConfirmSepaMandateRequest.make

type billingAccountData = {
  billingAccount: option<BillingAccount.info>,
  billingStatus: option<BillingAccount.BillingIssue.t>,
  subscriptions: option<BillingAccount.customerSubscriptions>,
}

@react.component
let make = (
  ~shopId,
  ~confirmSepaModalOpened,
  ~editPaymentMethodModalOpened,
  ~billingAccountRequest,
  ~billingStatusRequest,
  ~sepaMandateRequest,
  ~updatePaymentMethodRequest,
  ~invoicesRequest,
  ~subscriptionsRequest,
  ~confirmSepaMandateRequest,
  ~ipAddressRequest,
  ~reloadAlertBar,
  ~notification=?,
) => {
  let navigate = Navigation.useNavigate()

  let {billingAccountShowRoute, billingAccountShowRouteWithModal} = module(SettingsRoutes)
  let onRequestCloseModal = () => navigate(billingAccountShowRoute(~shopId, ()), ~replace=true)
  let handleOpenConfirmMandateModal = () =>
    navigate(billingAccountShowRouteWithModal(~modal=ConfirmMandate, ~shopId), ~replace=true)
  let handleOpenEditPaymentMethodModal = () =>
    navigate(billingAccountShowRouteWithModal(~modal=EditPaymentMethod, ~shopId), ~replace=true)

  let shops = Auth.useShops()
  let currentShop = shops->Array.getBy(shop => shop.id === shopId)->Option.getUnsafe
  let scope = Auth.useScope()
  let organisationAccount = switch scope {
  | Organisation(_) => true
  | Single(_) => false
  }

  let (state, setState) = React.useState(_ => AsyncResult.notAsked())

  React.useEffect1(() => {
    setState(_ => Loading)

    let future = Future.all3((
      billingAccountRequest(~shopId),
      billingStatusRequest(~shopId),
      subscriptionsRequest(~shopId),
    ))

    future->Future.get(results => {
      switch results {
      | (Ok(billingAccount), Ok(billingStatus), Ok(subscriptions)) =>
        setState(_ => Done(Ok({billingAccount, billingStatus, subscriptions})))
      | _ => setState(_ => Done(Error()))
      }
    })

    Some(() => future->Future.cancel)
  }, [currentShop])

  let reloadBillingAccount = () => {
    setState(AsyncResult.toBusy)

    let future = Future.all2((billingAccountRequest(~shopId), billingStatusRequest(~shopId)))

    future->Future.map(results => {
      setState(prevState =>
        switch (results, prevState) {
        | (
            (Ok(billingAccount), Ok(billingStatus)),
            Reloading(Ok({subscriptions})) | Done(Ok({subscriptions})),
          ) =>
          Done(Ok({billingAccount, billingStatus, subscriptions}))
        | _ => Done(Error())
        }
      )
    })
  }

  let onChangeShop = (value: Auth.shop) =>
    navigate(billingAccountShowRoute(~shopId=value.id, ()), ~replace=true)

  let headerActions = if organisationAccount {
    <Inline>
      <Auth.SelectSingleShopFilter onChange=onChangeShop value=currentShop />
    </Inline>
  } else {
    React.null
  }

  <Page
    title={t("Subscription and billing")}
    renderHeaderActions={() => headerActions}
    variation=#compact>
    {switch notification {
    | Some(notification) =>
      <Box spaceTop=#large>
        <Banner textStatus={notification} />
      </Box>
    | None => React.null
    }}
    {switch state {
    | Done(Error(_)) | Reloading(Error(_)) =>
      let errorMessage = "An unexpected error occured. Please try again or contact the support."
      <Box spaceTop=#large>
        <Banner textStatus={Danger(t(errorMessage))} />
      </Box>
    | Done(Ok({subscriptions, billingAccount, billingStatus}))
    | Reloading(Ok({subscriptions, billingAccount, billingStatus})) =>
      <>
        <WarningNotificationBanner billingAccount />
        <Box spaceTop=#large>
          <Group wrap=false grow=false grid=["30%", "70%"] spaceX=#large>
            <Stack space=#medium>
              <SubscriptionsCard subscriptions />
              <PaymentMethodCard
                editPaymentMethodModalOpened
                confirmSepaModalOpened
                sepaMandateRequest
                activeShopId=shopId
                shopName=currentShop.name
                handleOpenEditPaymentMethodModal
                handleOpenConfirmMandateModal
                billingStatus
                billingAccount
                updatePaymentMethodRequest
                confirmSepaMandateRequest
                onRequestCloseModal
                ipAddressRequest
                reloadBillingAccount
                reloadAlertBar
              />
              <BillingInformationCard activeShopId=shopId billingAccount />
            </Stack>
            <BillingInvoicesCard shopId invoicesRequest />
          </Group>
        </Box>
      </>
    | NotAsked | Loading => <Placeholder status=Loading />
    }}
  </Page>
}

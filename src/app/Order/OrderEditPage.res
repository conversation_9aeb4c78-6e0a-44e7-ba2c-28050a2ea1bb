open Intl

@react.component
let make = (
  ~id,
  ~name,
  ~updatedAt,
  ~statuses,
  ~receptionFinishedAt,
  ~cartRowErrors,
  ~onRequestCartRowsError,
) => {
  let {pathname} = Navigation.useUrl()
  let notifier = Notifier.use()
  let state = OrderEditForm.useFormState()
  let dispatch = OrderEditForm.useFormDispatch()

  let (edition, setEdition) = React.useState(() =>
    OrderEdit.isInitialFullEdition(~statuses, ~created=updatedAt->Option.isSome)
  )
  let (editedMemo, setEditedMemo) = React.useState(() => false)

  React.useEffect2(() => {
    if !edition {
      notifier.clear()
      onRequestCartRowsError([])
    }
    None
  }, (updatedAt, edition))

  React.useEffect1(() => {
    if statuses->OrderStatus.has(#RECEIVING) {
      setEdition(_ => true)
    }
    None
  }, [statuses->OrderStatus.has(#RECEIVING)])

  React.useEffect1(() => {
    switch state.submission {
    | Succeeded(_) => setEdition(_ => false)
    | _ => ()
    }
    None
  }, [state.submission])

  React.useEffect1(() => {
    if state.status !== Pristine && state.submission !== Requested {
      setEdition(_ => true)
    } else {
      setEditedMemo(_ => false)
    }
    None
  }, [state.status])

  // NOTE - specific edition state for memo field to not trigger global edition mode
  React.useEffect1(() => {
    if !edition && state.values.note !== state.initialValues.note {
      setEditedMemo(_ => true)
    }
    None
  }, [state.values.note])

  let onRequestDispatch = action =>
    FieldValueChanged(Cart, prevCart => prevCart->Accounting.Reducer.reducer(action))->dispatch

  let renderPageTitleEnd = () =>
    switch edition && !editedMemo {
    | false => <OrderStatusBadges statuses />
    | _ => React.null
    }

  let handlesActionList = React.useCallback4(action =>
    switch action {
    | OrderEditActionsList.Edition => setEdition(_ => true)
    | DownloadPDF =>
      switch state.id {
      | Some(orderId) =>
        OrderPdf.requestAndOpen(~orderId, ~orderName=name)
        ->Future.tapError(_ => notifier.add(Error(OrderPdf.errorMessage), ()))
        ->ignore

        notifier.clear()
      | None => ()
      }
    | DownloadCSV =>
      switch state.values.cart->OrderCartCsvExporting.makeBlob(
        ~name,
        ~statuses,
        ~supplierName=state.values.supplierName,
        ~supplierAddress=state.values.supplierAddress,
        ~supplierPostalCode=state.values.supplierPostalCode,
        ~supplierCity=state.values.supplierCity,
        ~supplierCountry=state.values.supplierCountry,
      ) {
      | Ok(csvBlob) =>
        csvBlob
        ->TriggerDownload.fromBlob(~filename=OrderCartCsvExporting.makeFilename(~statuses, ~name))
        ->Future.tapError(() => notifier.add(Error("Something went wrong"), ()))
        ->ignore
      | _ => notifier.add(Error("Something went wrong"), ())
      }
    }
  , (state.id, state.values.cart, name, statuses))

  let renderPageActions = () =>
    <Inline space=#small>
      {switch edition {
      | false => <OrderEditActionsList id statuses onRequestAction=handlesActionList />
      | _ => React.null
      }}
      <OrderPageActions id statuses edition onRequestCancelEdition={() => setEdition(_ => false)} />
    </Inline>

  let shouldBlockOnRouteChange = React.useCallback1(nextRoute => {
    // NOTE - don't block at order creation when route received order id
    let orderBaseRoute = Order->LegacyRouter.routeToPathname
    let allowed =
      pathname === orderBaseRoute ++ "/create" &&
        nextRoute->Js.String2.startsWith(orderBaseRoute ++ "/")

    !allowed && state.status !== Pristine
  }, [state.status])

  let editable = statuses->OrderStatus.has(#DRAFT) || (edition && !editedMemo)
  let formattedTitle = template(
    t("Order{{entilted}}{{name}}"),
    ~values={
      "entilted": switch statuses->Array.getExn(0) {
      | #RECEIVING if editable => t("Reception")
      | _ if edition && !editedMemo => t("Edition")
      | _ => " "
      },
      "name": t(name),
    },
    (),
  )

  <>
    <Navigation.Prompt
      message={t("Some changes in the order have not been saved.")} shouldBlockOnRouteChange
    />
    <Page title=formattedTitle renderTitleEnd=renderPageTitleEnd renderActions=renderPageActions>
      <Stack space=#medium>
        <Notifier.Banner notifier />
        <OrderCart
          orderId=id
          cart=state.values.cart
          statuses
          editable
          edition
          rowErrors=cartRowErrors
          onRequestCancelEdition={() => setEdition(_ => false)}
          onRequestCartRowsError
          onRequestDispatch
        />
        <Group wrap=false grid=["30%", "70%"] spaceX=#medium>
          <Stack space=#medium>
            <OrderSupplierInformationCard editable={statuses->OrderStatus.has(#DRAFT)} />
            <OrderInformationCard
              editable={editable && !OrderEdit.isLimitedEdition(~statuses)}
              receptionFinishedAt
              updatedAt
            />
            <OrderMemoCard />
          </Stack>
          <Stack space=#medium>
            <OrderConditionAndNotesCard
              editable={editable && !OrderEdit.isLimitedEdition(~statuses)}
            />
          </Stack>
        </Group>
      </Stack>
    </Page>
  </>
}

// let make = React.memoCustomCompareProps(make, (oldProps, newProps) =>
//   oldProps["id"] === newProps["id"] &&
//   oldProps["name"] === newProps["name"] &&
//   oldProps["updatedAt"] === newProps["updatedAt"] &&
//   oldProps["statuses"] === newProps["statuses"] &&
//   oldProps["cartRowErrors"] === newProps["cartRowErrors"]
// )

let make = React.memoCustomCompareProps(make, (oldProps, newProps) =>
  oldProps.id == newProps.id &&
  oldProps.name == newProps.name &&
  oldProps.updatedAt == newProps.updatedAt &&
  oldProps.statuses == newProps.statuses &&
  oldProps.cartRowErrors == newProps.cartRowErrors
)

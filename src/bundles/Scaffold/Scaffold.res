module ResetFiltersButton = {
  @react.component
  let make = React.memo((~onPress) =>
    <Box spaceLeft=#small>
      <TextIconButton size=#small icon=#close_light onPress={_ => onPress()}>
        {Intl.t("Reset")->React.string}
      </TextIconButton>
    </Box>
  )
}

let edgesPerPage = 10

type connectionArguments = {first?: int, last?: int, after?: string, before?: string}

type column<'row> = {
  name?: string,
  layout?: Table.layout,
  render: 'row => React.element,
}

type state<'filters> = {
  currentPage: int,
  previousPage: int,
  searchQuery: option<string>,
  filters: 'filters,
  connectionArguments: connectionArguments,
}

let makeTotalPages = (totalCount, nodesPerPage) =>
  switch nodesPerPage {
  | _ if nodesPerPage <= 0 => 1
  | _ if totalCount <= 0 => 1
  | nodesPerPage =>
    mod(totalCount, nodesPerPage) === 0 ? totalCount / nodesPerPage : totalCount / nodesPerPage + 1
  }

let useQueryStringPersistReducer = (codec, reducer, initialState) => {
  let navigate = Navigation.useNavigate()
  let url = Navigation.useUrl()

  let initialState = React.useMemo0(() => {
    let urlState = url.query->QueryString.parse->JsonCodec.decodeWith(codec)

    switch (urlState, url.query->QueryString.toString) {
    | (Ok(_), "") | (Error(_), _) => initialState
    | (Ok(state), _) => state
    }
  })

  let (state, dispatch) = React.useReducer(reducer, initialState)

  React.useEffect1(() => {
    let query = state->JsonCodec.encodeWith(codec)->QueryString.stringify
    let route = url.pathname ++ "?" ++ query->QueryString.toString

    if query !== url.query {
      navigate(route, ~replace=true)
    }
    None
  }, [state])

  (state, dispatch)
}

module type Config = {
  type filters
  let useFiltersJsonCodec: unit => JsonCodec.t<filters>

  module QueryInner: {
    type t
    type t_variables

    module Raw: {
      type t
      type t_variables
    }
  }

  type queryVariableFilterBy
  let useQuery: (
    ~client: ApolloClient__React_Hooks_UseQuery.ApolloClient.t=?,
    ~context: Json.t=?,
    ~displayName: string=?,
    ~errorPolicy: ApolloClient__React_Hooks_UseQuery.ErrorPolicy.t=?,
    ~fetchPolicy: ApolloClient__React_Hooks_UseQuery.WatchQueryFetchPolicy.t=?,
    ~nextFetchPolicy: ApolloClient__React_Hooks_UseQuery.WatchQueryFetchPolicy.t=?,
    ~mapJsVariables: QueryInner.Raw.t_variables => QueryInner.Raw.t_variables=?,
    ~notifyOnNetworkStatusChange: bool=?,
    ~onCompleted: ApolloClient__React_Hooks_UseQuery.Types.parseResult<QueryInner.t> => unit=?,
    ~onError: ApolloClient__React_Hooks_UseQuery.ApolloError.t => unit=?,
    ~partialRefetch: bool=?,
    ~pollInterval: int=?,
    ~skip: bool=?,
    ~ssr: bool=?,
    QueryInner.t_variables,
  ) => ApolloClient__React_Hooks_UseQuery.QueryResult.t<
    QueryInner.t,
    QueryInner.Raw.t,
    QueryInner.t_variables,
    QueryInner.Raw.t_variables,
  >

  let makeQueryVariables: (
    QueryInner.t_variables,
    ~connectionArguments: connectionArguments,
    ~search: string=?,
    ~filterBy: queryVariableFilterBy=?,
    unit,
  ) => QueryInner.t_variables

  let makeQueryVariablesFilterBy: filters => queryVariableFilterBy
  let cursorsFromQueryData: QueryInner.t => (option<string>, option<string>)
  let totalCountFromQueryData: QueryInner.t => int

  type row

  let rowsFromQueryDataAndState: (QueryInner.t, state<filters>) => array<row>
  let keyExtractor: row => string
}

module Make = (Config: Config) => {
  let {
    makeQueryVariables,
    makeQueryVariablesFilterBy,
    cursorsFromQueryData,
    totalCountFromQueryData,
    rowsFromQueryDataAndState,
    keyExtractor,
    useFiltersJsonCodec,
  } = module(Config)

  type filters = Config.filters

  module CodecState = {
    let encoder = ({currentPage, previousPage, searchQuery, filters, connectionArguments}) => (
      Some(currentPage),
      Some(previousPage),
      searchQuery,
      Some(filters),
      connectionArguments.first,
      connectionArguments.last,
      connectionArguments.after,
      connectionArguments.before,
    )

    let decoder = (
      initialFilters,
      (
        currentPage,
        previousPage,
        searchQuery,
        filters,
        connectionArgumentsFirst,
        connectionArgumentsLast,
        connectionArgumentsAfter,
        connectionArgumentsBefore,
      ),
    ) => Ok({
      currentPage: currentPage->Option.getWithDefault(1),
      previousPage: previousPage->Option.getWithDefault(-1),
      searchQuery,
      filters: filters->Option.getWithDefault(initialFilters),
      connectionArguments: {
        first: ?connectionArgumentsFirst,
        last: ?connectionArgumentsLast,
        after: ?connectionArgumentsAfter,
        before: ?connectionArgumentsBefore,
      },
    })

    let value = (~filtersCodec, ~initialFilters) =>
      JsonCodec.object8(
        encoder,
        decoder(initialFilters),
        JsonCodec.field("page", JsonCodec.int)->JsonCodec.optional,
        JsonCodec.field("previousPage", JsonCodec.int)->JsonCodec.optional,
        JsonCodec.field("search", JsonCodec.string)->JsonCodec.optional,
        JsonCodec.field("filters", filtersCodec)->JsonCodec.optional,
        JsonCodec.field("first", JsonCodec.int)->JsonCodec.optional,
        JsonCodec.field("last", JsonCodec.int)->JsonCodec.optional,
        JsonCodec.field("after", JsonCodec.string)->JsonCodec.optional,
        JsonCodec.field("before", JsonCodec.string)->JsonCodec.optional,
      )
  }

  type action =
    | Navigated({
        nextPage: int,
        queryTotalCount: int,
        queryCursors: (option<string>, option<string>),
      })
    | Searched(string)
    | FiltersUpdated(filters => filters)
    | Reset(state<filters>)

  let makeInitialState = (~filters) => {
    currentPage: 1,
    previousPage: -1,
    searchQuery: None,
    filters,
    connectionArguments: {first: 10},
  }

  let makeConnectionArguments = (~currentPage, ~previousPage, ~totalCount, ~cursors) =>
    switch (currentPage, cursors) {
    | (1, _) => Some({first: edgesPerPage})
    | (currentPage, (Some(startCursor), _)) if currentPage === previousPage - 1 =>
      Some({last: edgesPerPage, before: startCursor})
    | (currentPage, (_, Some(endCursor))) if currentPage === previousPage + 1 =>
      Some({first: edgesPerPage, after: endCursor})
    | (currentPage, _) if currentPage === makeTotalPages(totalCount, edgesPerPage) =>
      Some({last: edgesPerPage})
    | _ => None
    }

  let use = initialState => {
    let reducer = (state, action) =>
      switch action {
      | Navigated({nextPage, queryTotalCount: totalCount, queryCursors: cursors}) =>
        let currentPage = nextPage
        let previousPage = state.currentPage
        let maybeConnectionArguments = makeConnectionArguments(
          ~currentPage,
          ~previousPage,
          ~totalCount,
          ~cursors,
        )

        switch maybeConnectionArguments {
        | Some(connectionArguments) => {
            ...state,
            currentPage,
            previousPage,
            connectionArguments,
          }
        | None => state
        }
      | Searched(searchQuery) => {
          searchQuery: switch searchQuery {
          | "" => None
          | _ => Some(searchQuery)
          },
          currentPage: 1,
          previousPage: -1,
          connectionArguments: {first: 10},
          filters: state.filters,
        }
      | FiltersUpdated(updateStateFilters) => {
          filters: updateStateFilters(state.filters),
          currentPage: 1,
          previousPage: -1,
          connectionArguments: {first: 10},
          searchQuery: state.searchQuery,
        }
      | Reset(newState) => newState
      }

    let codec = CodecState.value(
      ~filtersCodec=useFiltersJsonCodec(),
      ~initialFilters=initialState().filters,
    )

    useQueryStringPersistReducer(codec, reducer, initialState())
  }

  let makeNextPage = (~state, ~action, ~totalPages) =>
    switch (action, state.currentPage, totalPages) {
    | (_, 0 | 1, 0 | 1) => None
    | (LegacyPagination.First, currentPage, _) if currentPage >= 1 => Some(1)
    | (Prev, currentPage, totalPages) if currentPage > totalPages => Some(totalPages)
    | (Prev, currentPage, _) if currentPage > 1 => Some(currentPage - 1)
    | (Next, currentPage, totalPages) if currentPage > totalPages => Some(totalPages)
    | (Next, currentPage, _) if currentPage >= 1 && currentPage < totalPages =>
      Some(currentPage + 1)
    | (Last, currentPage, _) if currentPage >= 1 => Some(totalPages)
    | _ => None
    }

  @react.component
  let make = (
    ~title,
    ~subtitle=?,
    ~renderTitleEnd=?,
    ~state: state<Config.filters>,
    ~dispatch,
    ~columns,
    ~filters=React.null,
    ~actions=React.null,
    ~banner=React.null,
    ~searchBar=React.null,
    ~emptyState,
    ~defaultQueryVariables,
  ) => {
    let queryVariables = React.useMemo2(() => {
      let filterBy = makeQueryVariablesFilterBy(state.filters)

      makeQueryVariables(
        defaultQueryVariables,
        ~search=?state.searchQuery,
        ~connectionArguments=state.connectionArguments,
        ~filterBy,
        (),
      )
    }, (defaultQueryVariables, state))

    let asyncResult =
      Config.useQuery(queryVariables)
      ->ApolloHelpers.queryResultToAsyncResult
      ->AsyncResult.mapError(_ => ())

    let onRequestPaginate = React.useCallback2(action =>
      switch asyncResult {
      | Reloading(Ok(queryData)) | Done(Ok(queryData)) =>
        let totalPages = makeTotalPages(queryData->totalCountFromQueryData, edgesPerPage)
        let maybeNextPage = makeNextPage(~state, ~action, ~totalPages)

        switch maybeNextPage {
        | Some(nextPage) =>
          let queryTotalCount = queryData->totalCountFromQueryData
          let queryCursors = queryData->cursorsFromQueryData

          Navigated({nextPage, queryTotalCount, queryCursors})->dispatch
        | None => ()
        }
      | _ => ()
      }
    , (state.currentPage, asyncResult))

    let pageVariation = switch subtitle {
    | Some(_) => #standard
    | None => #compact
    }

    let asyncTableRows =
      asyncResult->AsyncResult.mapOk(queryData => rowsFromQueryDataAndState(queryData, state))

    let columns = columns->Array.mapWithIndex((index, column) => {
      Table.key: column.name->Option.getWithDefault("scaffold-key-" ++ index->Int.toString),
      name: column.name->Option.getWithDefault(""),
      layout: ?column.layout,
      render: row => column.render(row.data),
    })

    let searchBar = <Box spaceX=#large spaceBottom=#xmedium> searchBar </Box>

    <Page title ?subtitle ?renderTitleEnd variation=pageVariation>
      <BarControl filters actions banner />
      <TableView
        columns
        data=asyncTableRows
        keyExtractor
        placeholderEmptyState=emptyState
        hideReloadingPlaceholder=true
        searchBar
        onSearchQueryChange={searchQuery => Searched(searchQuery)->dispatch}
      />
      {switch asyncResult {
      | Done(Ok(queryData)) | Reloading(Ok(queryData)) =>
        <LegacyPagination
          currentPage=state.currentPage
          totalPages={makeTotalPages(queryData->totalCountFromQueryData, edgesPerPage)}
          onRequestPaginate
        />
      | _ => <LegacyPagination currentPage=1 totalPages=1 onRequestPaginate />
      }}
    </Page>
  }

  // let make = React.memoCustomCompareProps(make, (oldProps, newProps) =>
  //   oldProps["title"] === newProps["title"] &&
  //   oldProps["subtitle"] === newProps["subtitle"] &&
  //   oldProps["state"] === newProps["state"] &&
  //   oldProps["filters"] === newProps["filters"] &&
  //   oldProps["actions"] === newProps["actions"] &&
  //   oldProps["columns"] === newProps["columns"] &&
  //   oldProps["banner"] === newProps["banner"] &&
  //   oldProps["searchBar"] === newProps["searchBar"] &&
  //   oldProps["emptyState"] === newProps["emptyState"] &&
  //   oldProps["defaultQueryVariables"] === newProps["defaultQueryVariables"]
  // )
}

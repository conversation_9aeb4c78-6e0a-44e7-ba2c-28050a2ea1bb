@genType
type variation = [#danger | #neutral | #primary | #secondary | #success | #warning]

@react.component
let make: (
  ~children: React.element,
  ~size: Sizes.t=?,
  ~variation: variation=?,
  ~betaBadge: bool=?,
  ~loading: bool=?,
  ~disabled: bool=?,
  ~focused: bool=?,
  ~icon: Icon.t=?,
  ~ariaProps: ReactAria.Button.props=?,
  ~onPress: ReactAria.Button.pressEvent => unit=?,
  ~ref: ReactDOM.Ref.currentDomRef=?,
) => React.element

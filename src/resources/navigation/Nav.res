open WebAPI
open Intl
open StyleX

module Context = {
  type action = Toggled

  type state = {opened: bool}

  let initialState = {opened: false}

  type dispatch = action => unit
  type context = (option<state>, dispatch)
  let context: React.Context.t<context> = React.createContext((None, _ => ignore()))
  let use = () => {
    let (state, dispatch) = React.useContext(context)
    switch state {
    | None => (initialState, _ => ())
    | Some(state) => (state, dispatch)
    }
  }

  module Provider = {
    let make = React.Context.provider(context)
  }

  let getReducer = _ => React.useReducer((state, action) =>
      switch action {
      | Toggled => {opened: !state.opened}
      }
    , initialState)
}

module HighlightLinkIcon = {
  @react.component
  let make = (~style=?) =>
    <Svg ?style width={30.->Float.toString} height={30.->Float.toString} viewBox="0 0 20 20">
      <Svg.Path
        d="M2 6C2 3.79086 3.79086 2 6 2C8.20914 2 10 3.79086 10 6C10 8.20914 8.20914 10 6 10C3.79086 10 2 8.20914 2 6Z"
        fill=Colors.neutralColor00
      />
      <Svg.Path
        d="M6 10V8C4.89543 8 4 7.10457 4 6H2H0C0 9.31371 2.68629 12 6 12V10ZM10 6H8C8 7.10457 7.10457 8 6 8V10V12C9.31371 12 12 9.31371 12 6H10ZM6 2V4C7.10457 4 8 4.89543 8 6H10H12C12 2.68629 9.31371 0 6 0V2ZM6 2V0C2.68629 0 0 2.68629 0 6H2H4C4 4.89543 4.89543 4 6 4V2Z"
        fill=Colors.neutralColor00
        transform="scale(0.9)"
      />
      <Svg.Path
        d="M6 2.40039C7.98822 2.40039 9.59961 4.01177 9.59961 6C9.59961 7.98822 7.98822 9.59961 6 9.59961C4.01177 9.59961 2.40039 7.98822 2.40039 6C2.40039 4.01177 4.01177 2.40039 6 2.40039ZM6 6.7998C5.77909 6.7998 5.59961 6.97928 5.59961 7.2002C5.59971 7.42102 5.77915 7.59961 6 7.59961H6.00293C6.22378 7.59961 6.40321 7.42102 6.40332 7.2002C6.40332 6.97928 6.22384 6.7998 6.00293 6.7998H6ZM6 4.40039C5.77915 4.40039 5.59971 4.57898 5.59961 4.7998V6C5.59961 6.22091 5.77909 6.40039 6 6.40039C6.22091 6.40039 6.40039 6.22091 6.40039 6V4.7998C6.40029 4.57898 6.22085 4.40039 6 4.40039Z"
        fill=Colors.dangerColor50
      />
    </Svg>
}

module NavTextIcon = {
  let styles = StyleX.create({
    "NavTextIcon_root": style(
      ~display=#flex,
      ~flex="1",
      ~flexDirection=#row,
      ~justifyContent=#"flex-start",
      ~alignItems=#center,
      ~minHeight="36px",
      (),
    ),
    "NavTextIcon_text": style(~marginLeft="4px", ~whiteSpace=#nowrap, ()),
    "textActive": style(~color=Colors.neutralColor90, ()),
    "textHovered": style(~color=Colors.neutralColor70, ()),
    "textImportant": style(~font=`normal 600 16px "Archivo"`, ~color=Colors.neutralColor00, ()),
    "textPrimary": style(~font=`normal 400 15px "Archivo"`, ~color=Colors.neutralColor50, ()),
  })

  let textStyleFromParams = (~variation, ~active, ~hovered) =>
    switch (variation, active, hovered) {
    | (#important, _, _) => styles["textImportant"]
    | (#primary, true, _) => StyleX.arrayStyle([styles["textPrimary"], styles["textActive"]])
    | (#primary, false, true) => StyleX.arrayStyle([styles["textPrimary"], styles["textHovered"]])
    | (_, _, _) => styles["textPrimary"]
    }

  let iconColorFromParams = (~active, ~hovered, navOpened) =>
    switch (active, hovered, navOpened) {
    | (true, _, false)
    | (true, _, true)
    | (false, true, false) => Colors.neutralColor90
    | (false, true, true) => Colors.neutralColor70
    | _ => Colors.neutralColor35
    }

  type variation = [#important | #primary | #secondary]

  @react.component
  let make = React.memo((
    ~children,
    ~icon,
    ~active=false,
    ~variation: variation=#primary,
    ~hovered=false,
    ~displayHighlightLinkIcon=?,
    ~disabled=false,
  ) => {
    let derivedHovered = hovered

    let (nav, _) = Context.use()
    let (ref, hovered) = Hover.use()

    <DivX ref style={StyleX.props([styles["NavTextIcon_root"]])}>
      <Box spaceX=#normal>
        <Icon
          name=icon
          fill={variation === #important
            ? Colors.neutralColor00
            : iconColorFromParams(~active, ~hovered=hovered || derivedHovered, nav.opened)}
        />
        {switch (!nav.opened, displayHighlightLinkIcon) {
        | (true, Some(true)) =>
          <HighlightLinkIcon
            style={ReactDOMStyle.make(~position="absolute", ~top="0px", ~right="-15px", ())}
          />
        | _ => React.null
        }}
      </Box>
      {if nav.opened {
        <SpanX
          style={StyleX.props([
            styles["NavTextIcon_text"],
            textStyleFromParams(~variation, ~active, ~hovered=hovered || derivedHovered),
          ])}>
          children
        </SpanX>
      } else {
        // NOTE - small hack until the Nav components suite get revamped (new UX, new architecture)
        let textContent = Obj.magic(children)->Js.String2.replace("&#x27;", "'")
        <Tooltip
          placement=#start
          offset={-4.}
          crossOffset={1.}
          delay=0
          content={<Tooltip.Span text={disabled ? t("Restricted access") : textContent} />}
          opened=hovered
        />
      }}
    </DivX>
  })
}

module NavSection = {
  let styles = StyleX.create({
    "NavSection_header": style(~justifyContent=#center, ~height="42px", ()),
    "NavSection_content": style(~maxHeight="999px", ~overflow=#hidden, ()),
    "contentClosed": style(~maxHeight="0px", ()),
    "NavSection_list": style(~paddingInline="12px", ~paddingBottom="4px", ()),
  })

  let contentStyleFromParams = (~opened) =>
    StyleX.props([styles["NavSection_content"], !opened ? styles["contentClosed"] : style()])

  @react.component
  let make = (~children, ~title, ~active, ~icon, ~displayHighlightLinkIcon=?, ~disabled=false) => {
    let nav = Context.use()->fst
    let url = Navigation.useUrl()

    let (ref, hovered) = Hover.use()
    let (opened, setOpened) = React.useState(() => false)
    let {ref: popoverTriggerRef, state: popover, ariaProps: popoverAriaProps} = Popover.useTrigger()

    React.useEffect1(() => {
      if !nav.opened {
        setOpened(_ => false)
      }
      None
    }, [nav.opened])

    React.useEffect1(() => {
      popover.onRequestClose()
      None
    }, [url.pathname])

    <>
      {if nav.opened {
        <Touchable disabled onPress={_ => setOpened(opened => !opened)}>
          <DivX ref style={StyleX.props([styles["NavSection_header"]])}>
            <Inline align=#spaceBetween>
              <NavTextIcon active icon hovered> {title->React.string} </NavTextIcon>
              <Icon
                name={opened ? #arrow_up_light : #arrow_down_light}
                fill={hovered || active ? Colors.neutralColor70 : Colors.neutralColor30}
              />
            </Inline>
            {switch displayHighlightLinkIcon {
            | Some(true) =>
              <HighlightLinkIcon
                style={ReactDOMStyle.make(~position="absolute", ~top="6px", ~right="-23px", ())}
              />
            | Some(false) | None => React.null
            }}
          </DivX>
        </Touchable>
      } else {
        <Touchable
          ariaProps=popoverAriaProps.triggerProps onPress={_ => popover.onRequestToggle()} disabled>
          <DivX ref=popoverTriggerRef style={StyleX.props([styles["NavSection_header"]])}>
            <NavTextIcon hovered active icon ?displayHighlightLinkIcon disabled>
              {title->React.string}
            </NavTextIcon>
          </DivX>
        </Touchable>
      }}
      <DivX style={contentStyleFromParams(~opened)}>
        <DivX style={StyleX.props([styles["NavSection_list"]])}> children </DivX>
      </DivX>
      {if popover.opened {
        <Popover
          triggerRef=popoverTriggerRef
          state=popover
          variation=#arrowed
          placement=#end
          offset=0.
          crossOffset=1.>
          <Popover.Dialog ariaProps=popoverAriaProps.overlayProps>
            <Box spaceX=#medium spaceY=#small> children </Box>
          </Popover.Dialog>
        </Popover>
      } else {
        React.null
      }}
    </>
  }

  let make = React.memo(make)
}

module NavButton = {
  let styles = StyleX.create({
    "label": style(~font=`normal 400 15px "Archivo"`, ()),
    "labelNormal": style(~color=Colors.neutralColor50, ()),
    "labelNormalHovered": style(~color=Colors.neutralColor70, ()),
    "labelImportant": style(~color=Colors.dangerColor50, ()),
    "labelImportantHovered": style(~color=Colors.dangerColor60, ()),
  })

  let labelStyleFromParams = (variation, hovered) =>
    StyleX.props([
      styles["label"],
      switch (variation, hovered) {
      | (#important, false) => styles["labelImportant"]
      | (#important, true) => styles["labelImportantHovered"]
      | (#normal, false) => styles["labelNormal"]
      | (#normal, true) => styles["labelNormalHovered"]
      },
    ])

  type variation = [#normal | #important]

  @react.component
  let make = React.memo((~label=?, ~onPress=?, ~icon=?, ~variation: variation=#normal) => {
    let (ref, hovered) = Hover.use()
    <DivX ref>
      {switch (label, onPress, icon) {
      | (Some(label), Some(onPress), None) =>
        <Touchable onPress={_ => onPress()}>
          <Box spaceY=#small>
            <SpanX style={labelStyleFromParams(variation, hovered)}> {label->React.string} </SpanX>
          </Box>
        </Touchable>
      | (Some(label), Some(onPress), Some(icon)) =>
        <Touchable onPress={_ => onPress()}>
          <NavTextIcon icon> {label->React.string} </NavTextIcon>
        </Touchable>
      | _ => React.null
      }}
    </DivX>
  })
}

module NavFooter = {
  @module("./Nav_default_profile.png") external defaultProfilePictureUri: string = "default"

  let containerSize = Spaces.xxlargePx

  let styles = StyleX.create({
    "NavFooter_view": style(
      ~height="60px",
      ~paddingInline=Spaces.mediumPx,
      ~paddingBlock=Spaces.xmediumPx,
      ~backgroundColor=Colors.backgroundDefaultColortemplate,
      (),
    ),
    "NavFooter_moreView": style(
      ~backgroundColor=Colors.backgroundDefaultColortemplate,
      ~borderTop="1px solid " ++ Colors.neutralColor15,
      (),
    ),
    "moreViewOpened": style(~maxHeight="300px", ~transition="max-height 0.55s", ()),
    "moreViewClosed": style(~maxHeight="0px", ~transition="max-height 0.15s", ()),
    "NavFooter_moreList": style(
      ~marginHorizontal="23px",
      ~paddingBlock=Spaces.mediumPx,
      ~paddingInline=Spaces.xxsmallPx,
      ~borderBottom="1px solid " ++ Colors.neutralColor15,
      (),
    ),
    "NavFooter_container": style(~display=#flex, ~alignItems=#center, ~height=containerSize, ()),
    "containerClosed": style(~width=containerSize, ()),
    "NavFooter_username": style(
      ~flex="1",
      ~paddingInline=Spaces.normalPx,
      ~font=`normal 400 15px "Archivo"`,
      ~color=Colors.neutralColor50,
      ~overflow=#hidden,
      ~width="100%",
      ~whiteSpace=#nowrap,
      ~textOverflow=#ellipsis,
      (),
    ),
  })

  let profilePictureFromParams = (~hovered, ~active) =>
    StyleX.props([
      style(
        ~width="30px",
        ~height="30px",
        ~borderRadius="50px",
        ~border="1px solid " ++ Colors.neutralColor15,
        (),
      ),
      hovered ? style(~border="1px solid " ++ Colors.neutralColor25, ()) : style(),
      active ? style(~border="1px solid " ++ Colors.neutralColor30, ()) : style(),
    ])

  let containerStyleFromParams = opened =>
    StyleX.props([styles["NavFooter_container"], !opened ? styles["containerClosed"] : style()])

  let moreViewStyleFromParams = opened =>
    StyleX.props([
      styles["NavFooter_moreView"],
      opened ? styles["moreViewOpened"] : styles["moreViewClosed"],
    ])

  @react.component
  let make = (~children, ~userName, ~userProfilePictureUri=?) => {
    let (nav, _) = Context.use()
    let url = Navigation.useUrl()
    let (refProfilePicture, hoveredProfilePicture) = Hover.use()
    let (moreOpened, setMoreOpened) = React.useState(() => false)
    let {ref: popoverTriggerRef, state: popover, ariaProps: popoverAriaProps} = Popover.useTrigger()

    React.useEffect1(() => {
      popover.onRequestClose()
      None
    }, [url.pathname])

    React.useEffect1(() => {
      if !nav.opened {
        setMoreOpened(_ => false)
      }
      None
    }, [nav.opened])

    let handleFooterPress = _ =>
      if nav.opened {
        setMoreOpened(moreOpened => !moreOpened)
      } else {
        popover.onRequestToggle()
      }

    <>
      <DivX style={moreViewStyleFromParams(moreOpened)}>
        <DivX style={StyleX.props([styles["NavFooter_moreList"]])}> children </DivX>
      </DivX>
      <Touchable ref=refProfilePicture onPress=handleFooterPress>
        <DivX style={StyleX.props([styles["NavFooter_view"]])}>
          <DivX ref=popoverTriggerRef style={containerStyleFromParams(nav.opened)}>
            <ImgX
              src={userProfilePictureUri->Option.getWithDefault(defaultProfilePictureUri)}
              style={profilePictureFromParams(
                ~hovered=hoveredProfilePicture && !nav.opened,
                ~active=popover.opened,
              )}
            />
            {if nav.opened {
              <>
                <SpanX style={StyleX.props([styles["NavFooter_username"]])}>
                  {userName->React.string}
                </SpanX>
                <IconButton
                  name={moreOpened ? #arrow_down_light : #arrow_up_light}
                  marginSize=#xsmall
                  color={hoveredProfilePicture ? Colors.neutralColor70 : Colors.neutralColor30}
                  hoveredColor=Colors.neutralColor70
                  onPress=handleFooterPress
                />
              </>
            } else if popover.opened {
              <Popover
                triggerRef=popoverTriggerRef
                state=popover
                variation=#arrowed
                placement=#end
                offset=8.>
                <Popover.Dialog ariaProps=popoverAriaProps.overlayProps>
                  <Box spaceX=#medium spaceY=#small> children </Box>
                </Popover.Dialog>
              </Popover>
            } else {
              React.null
            }}
          </DivX>
        </DivX>
      </Touchable>
    </>
  }

  let make = React.memo(make)
}

module NavLink = {
  let restrictedAccessIcon = (~hovered) =>
    <Tooltip content={<Tooltip.Span text={t("Restricted access")} />}>
      <Offset top={-1.} left={-2.} height=16. width=16.>
        <svg width="16px" height="16px" viewBox="0 0 20 20">
          <path
            fill={switch hovered {
            | true => Colors.neutralColor90
            | false => Colors.neutralColor50
            }}
            fillRule="evenodd"
            clipRule="evenodd"
            d="M10 2C12.2091 2 14 3.79086 14 6V8H15C16.1046 8 17 8.89543 17 10V16C17 17.1046 16.1046 18 15 18H5C3.89543 18 3 17.1046 3 16V10C3 8.89543 3.89543 8 5 8H6V6C6 3.79086 7.79086 2 10 2ZM5 16H15V10H5V16ZM10 4C8.89543 4 8 4.89543 8 6V8H12V6C12 4.89543 11.1046 4 10 4Z"
          />
        </svg>
      </Offset>
    </Tooltip>

  let styles = StyleX.create({
    "NavLink_view": style(~height="42px", ~borderRadius="5px", ()),
    "viewImportant": style(~backgroundColor=Colors.secondaryColor50, ()),
    "viewImportantHovered": style(~backgroundColor=Colors.secondaryColor65, ()),
    "viewPrimary": style(~backgroundColor=Colors.transparent, ()),
    "NavLink_label": style(
      ~paddingBlock="8px",
      ~font=`normal 400 15px "Archivo"`,
      ~color=Colors.neutralColor50,
      (),
    ),
    "labelActiveHovered": style(~color=Colors.neutralColor90, ()),
  })

  let viewStyleFromParams = (~variation, ~hovered, navOpened) =>
    StyleX.props([
      styles["NavLink_view"],
      switch (variation, hovered, navOpened) {
      | (#important, false, true)
      | (#important, false, false) =>
        styles["viewImportant"]
      | (#important, true, _) => styles["viewImportantHovered"]
      | (#secondary, _, _)
      | (#primary, _, _) =>
        styles["viewPrimary"]
      },
    ])

  let labelStyleFromParams = (~active, ~hovered) =>
    StyleX.props([
      styles["NavLink_label"],
      if (!active && hovered) || active && !hovered || (active && hovered) {
        styles["labelActiveHovered"]
      } else {
        style()
      },
    ])

  @react.component
  let make = (
    ~to,
    ~label,
    ~active,
    ~icon=#ticket_bold,
    ~betaBadge=false,
    ~displayHighlightLinkIcon=false,
    ~restrictedMode=false,
    ~disabled=false,
    ~variation=#secondary,
  ) => {
    let (ref, hovered) = Hover.use()
    let (nav, _) = Context.use()

    <Navigation.Link to disabled>
      <DivX ref>
        <Inline space=#medium align=#spaceBetween>
          {switch variation {
          | #secondary =>
            <Inline space=#small>
              <SpanX style={labelStyleFromParams(~active, ~hovered)}> {label->React.string} </SpanX>
              {if betaBadge {
                <Badge variation=#information size=#small> {t("BETA")->React.string} </Badge>
              } else {
                React.null
              }}
              {if restrictedMode {
                restrictedAccessIcon(~hovered)
              } else {
                React.null
              }}
              {if displayHighlightLinkIcon {
                <Tooltip content={<Tooltip.Span text={t("Pending action")} />}>
                  <Icon
                    name=#warning
                    fill={switch hovered {
                    | true => Colors.dangerColor60
                    | false => Colors.dangerColor50
                    }}
                    size=16.
                  />
                </Tooltip>
              } else {
                React.null
              }}
            </Inline>
          | #primary | #important =>
            <DivX style={viewStyleFromParams(~variation, ~hovered, nav.opened)}>
              <NavTextIcon variation icon disabled=restrictedMode>
                {label->React.string}
              </NavTextIcon>
            </DivX>
          }}
        </Inline>
      </DivX>
    </Navigation.Link>
  }
}

@inline let openedSize = 262.
@inline let closedSize = 64.

let styles = StyleX.create({
  "root": style(
    ~display=#flex,
    ~position=#fixed,
    ~zIndex=1,
    ~top="0px",
    ~left="0px",
    ~bottom="0px",
    ~overflow=#hidden,
    ~backgroundColor=Colors.neutralColor00,
    ~boxShadow=`0px 0px 10px 2px ${Colors.neutralColor15}`,
    ~width="64px",
    ~transition="width 0.25s",
    (),
  ),
  "rootOpened": style(~width=openedSize->Float.toString ++ "px", ()),
  "container": style(
    ~display=#flex,
    ~flexDirection=#column,
    ~flex="1",
    ~width=openedSize->Float.toString ++ "px",
    (),
  ),
  "header": style(
    ~display=#flex,
    ~justifyContent=#"space-between",
    ~alignItems=#center,
    ~height="60px",
    ~borderBottom="1px solid " ++ Colors.neutralColor15,
    (),
  ),
  "burger": style(~marginLeft="10px", ()),
  "userOrganizationName": style(~font=`normal 700 15px "Archivo"`, ~color=Colors.brandColor50, ()),
  "userOrganizationNameAndBadge": style(
    ~display=#flex,
    ~flexDirection=#column,
    ~alignItems=#"flex-end",
    ~paddingRight="12px",
    (),
  ),
  "content": style(
    ~flex="1",
    ~paddingLeft="8px",
    ~paddingRight="17px",
    ~paddingTop="16px",
    ~width=closedSize->Float.toString ++ "px",
    ~overflow=#auto,
    ~overflowX=#hidden,
    ~scrollbarWidth="thin",
    ~transition="width 0.25s",
    (),
  ),
  "contentOpened": style(~width="100%", ()),
})

let rootStyleFromParams = (~opened) =>
  StyleX.props([styles["root"], opened ? styles["rootOpened"] : style()])

let contentStyleFromParams = opened =>
  StyleX.props([styles["content"], opened ? styles["contentOpened"] : style()])

let contentScrollShadeOverlay = ReactDOM.Style.make(
  ~height="16px",
  ~marginTop="-16px",
  ~borderBottom="1px solid " ++ Colors.neutralColor10,
  ~boxSizing="border-box",
  ~backgroundImage="radial-gradient(ellipse at center, rgba(0, 0, 0, 0.1) 0%, rgba(0, 0, 0, 0) 75%)",
  ~backgroundSize="100% 200%",
  ~backgroundPosition="center top",
  ~pointerEvents="none",
  (),
)

@react.component
let make = (
  ~children,
  ~userOrganizationName,
  ~userName,
  ~userProfilePictureUri=?,
  ~badge=?,
  ~userImpersonating,
  ~userSettingsRoute,
  ~helpCenterUrl,
  ~legacyDashboardUrl,
  ~legacyDashboardText,
  ~onToggleHelpCenter,
  ~onRequestLogout,
) => {
  let (nav, dispatch) = Context.use()
  let {pathname: activeRoute} = Navigation.useUrl()

  let (scrollable, setScrollable) = React.useState(() => false)
  let ref = React.useRef(Js.Nullable.null)

  let handleScrollable = React.useCallback1(() =>
    switch ref.current->Js.Nullable.toOption {
    | Some(domElement) =>
      let hasNotReachedBottom =
        domElement->DomElement.scrollTop->Float.toInt + domElement->DomElement.clientHeight <
          domElement->DomElement.scrollHeight

      setScrollable(_ => hasNotReachedBottom)
    | None => ()
    }
  , [ref])

  React.useLayoutEffect0(() => {
    let bodyDomElement = ref->ReactDomElement.fromRef
    handleScrollable()
    bodyDomElement->Option.forEach(domNode =>
      domNode->DomElement.addEventListener("scroll", handleScrollable)
    )
    Some(
      () =>
        bodyDomElement->Option.forEach(domEmement =>
          domEmement->DomElement.removeEventListener("scroll", handleScrollable)
        ),
    )
  })

  ReactAria.Resize.useObserver({
    ref: ref->ReactDOM.Ref.domRef,
    onResize: handleScrollable,
  })

  <DivX style={rootStyleFromParams(~opened=nav.opened)}>
    <DivX style={StyleX.props([styles["container"]])}>
      <DivX style={StyleX.props([styles["header"]])}>
        <DivX style={StyleX.props([styles["burger"]])}>
          <Burger onPress={_ => dispatch(Toggled)} />
        </DivX>
        {if nav.opened {
          <DivX style={StyleX.props([styles["userOrganizationNameAndBadge"]])}>
            <SpanX style={StyleX.props([styles["userOrganizationName"]])}>
              {userOrganizationName->React.string}
            </SpanX>
            {switch badge {
            | Some(badge) => <Box spaceTop=#xsmall> badge </Box>
            | None => React.null
            }}
          </DivX>
        } else {
          React.null
        }}
      </DivX>
      <DivX ref style={contentStyleFromParams(nav.opened)}> {children} </DivX>
      <AnimatedRender displayed=scrollable animation=#fade duration=250>
        <div style=contentScrollShadeOverlay />
      </AnimatedRender>
      {if !userImpersonating {
        <Box spaceX=#small spaceY=#small>
          <NavButton label={t("Contact us")} icon=#help_bold onPress={_ => onToggleHelpCenter()} />
          <Navigation.Link to=Url(helpCenterUrl) openNewTab=true>
            <NavTextIcon icon=#book_bold> {t("Help center")->React.string} </NavTextIcon>
          </Navigation.Link>
          <Navigation.Link to=Url(legacyDashboardUrl) openNewTab=true>
            <NavTextIcon icon=#switch_bold> {legacyDashboardText->React.string} </NavTextIcon>
          </Navigation.Link>
        </Box>
      } else {
        React.null
      }}
      <NavFooter
        userName
        userProfilePictureUri=?{userImpersonating
          ? Some("/impersonating_user.png")
          : userProfilePictureUri}>
        <NavLink
          to={Navigation.Route(userSettingsRoute)}
          active={activeRoute === userSettingsRoute}
          label={t("Your account")}
        />
        <NavButton label={t("Sign out")} variation=#important onPress={_ => onRequestLogout()} />
      </NavFooter>
    </DivX>
  </DivX>
}

let makeLabel: (string, ~required: bool) => string

module Action: {
  type handler =
    | Callback(unit => unit)
    | OpenLink(Navigation.to)
    | OpenLinkNewTab(Navigation.to)

  type t = {
    text: string,
    handler: handler,
  }
}

@react.component
let make: (
  ~children: React.element,
  ~label: string=?,
  ~labelAriaProps: JsxDOM.domProps=?,
  ~tooltip: React.element=?,
  ~errorMessage: string=?,
  ~action: Action.t=?,
  ~required: bool=?,
  ~ref: ReactDOM.Ref.currentDomRef=?,
) => React.element

open Intl
open Accounting.Types
open Accounting.Actions

module CartTable = OrderCartTable
module CartConfig = OrderCart__Config
module Utils = OrderEdit__Utils

module SupplierNameFragment = %graphql(`
  fragment supplier on Supplier {
    id
    companyName
  }
`)

module SupplierVariantsCountQuery = %graphql(`
  query SupplierQuery($id: ID!) {
    supplier(id: $id) {
      id
      variants {
        totalCount
      }
    }
  }
`)

module TaxesQuery = %graphql(`
  query TaxesQuery {
    taxes(first: 50) {
      edges {
        node {
          value
        }
      }
    }
  }
`)

module CartPlaceholder = {
  let styles = StyleX.create({
    "emptyDataView": StyleX.style(
      ~display=#flex,
      ~flexDirection=#column,
      ~alignItems=#center,
      ~backgroundColor=Colors.neutralColor05,
      ~paddingBlock="23px",
      ~borderBottom="1px solid " ++ Colors.neutralColor15,
      (),
    ),
  })

  module Loading = {
    @react.component
    let make = React.memo((~supplierName, ()) =>
      <DivX style={StyleX.props([styles["emptyDataView"]])}>
        <Spinner size=45. />
        <Box spaceY=#normal>
          <Title level=#3>
            {template(
              t("Search for {{supplier}} products in progress..."),
              ~values={"supplier": supplierName},
              (),
            )->React.string}
          </Title>
          <Box spaceTop=#xsmall />
          <TextStyle align=#center>
            {t("Analyzing products and their stock")->React.string}
          </TextStyle>
        </Box>
      </DivX>
    )
  }

  module Default = {
    @react.component
    let make = React.memo((~disabled, ~errorMessage=?, ~onPressCreateOrder, ()) =>
      <DivX style={StyleX.props([styles["emptyDataView"]])}>
        <InlineText>
          <TextStyle size=#xsmall variation=#normal>
            {(t("To save typing time, opt for the") ++ " ")->React.string}
          </TextStyle>
          <TextAction
            size=#compact
            highlighted=true
            text={t("automatic order")}
            onPress={() => HelpCenter.showArticle(HelpCenter.automaticOrdering)}
          />
          <TextStyle size=#xsmall variation=#normal> {" :"->React.string} </TextStyle>
        </InlineText>
        <Tooltip
          content={<Tooltip.Span text={t("Please select a supplier linked to the order.")} />}
          delay=0
          disabled={!disabled}>
          <Box spaceTop=#normal spaceBottom=#xsmall>
            <Button variation=#neutral disabled onPress={_ => onPressCreateOrder()}>
              {t("Create automatic order")->React.string}
            </Button>
          </Box>
        </Tooltip>
        {switch errorMessage {
        | Some(error) =>
          <Box spaceTop=#normal>
            <TextStyle size=#xsmall variation=#negative> {error->React.string} </TextStyle>
          </Box>
        | None => React.null
        }}
      </DivX>
    )
  }
}

@react.component
let make = (
  ~orderId,
  ~cart,
  ~statuses,
  ~editable,
  ~rowErrors,
  ~edition,
  ~onRequestCancelEdition,
  ~onRequestCartRowsError,
  ~onRequestDispatch,
) => {
  let ({Nav.Context.opened: navOpened}, _) = Nav.Context.use()
  let notifier = Notifier.use()
  let formState = OrderEditForm.useFormState()
  let (autoPickingStatus, setAutoPickingStatus) = React.useState(() =>
    OrderProductFormAutoPicker.Pristine
  )
  let (supplierProductsModalOpened, setSupplierProductsModalOpened) = React.useState(() => false)
  let (state, dispatch) = React.useReducer(CartConfig.reducer, CartConfig.initialState)
  let apolloClient = ApolloClient.React.useApolloClient()
  let captureEvent = SessionTracker.useCaptureEvent()

  let standardVAT = switch TaxesQuery.use(
    ~skip=orderId->Option.isSome,
    (),
  )->ApolloHelpers.queryResultToAsyncResult {
  | Done(Ok({taxes})) =>
    Some(taxes.edges->Array.map(edge => edge.node.value)->Js.Math.maxMany_float)
  | _ => None
  }

  // Gets supplierName from cache by its id
  let supplierId = formState.values.supplierId
  let supplierName = React.useMemo1(() =>
    apolloClient.readFragment(
      ~fragment=module(SupplierNameFragment),
      // TODO
      // 1. "Supplier" string should be extracted from
      //    `SupplierFragment` module via the __typename field
      // 2. The concatenation `${__typename}:${id}` should be made
      //    with a global util `Apollo.makeFragmentId`
      // 3. In the future move to ReScript Relay to have
      //    better fragment support
      ~id="Supplier:" ++ supplierId,
      (),
    )->Option.flatMap(result =>
      switch result {
      | Ok(supplier) => Some(supplier.companyName)
      | Error(_) => None
      }
    )
  , [supplierId])

  let supplierVariantsCount = switch SupplierVariantsCountQuery.use(
    {id: supplierId},
    ~skip=supplierId === "",
  ) {
  | {data: Some({supplier: Some({variants})})} => Some(variants.totalCount)
  | _ => None
  }->Option.getWithDefault(0)

  React.useEffect1(() => {
    switch standardVAT {
    | Some(standardVAT) => onRequestDispatch(StandardTaxRateUpdated(standardVAT))
    | None => ()
    }
    None
  }, [standardVAT])

  React.useEffect1(() => {
    switch autoPickingStatus {
    | Success(0) =>
      notifier.reset(
        Warning(
          template(
            t(
              "No products matching the set stock thresholds were found for the supplier {{supplier}}.",
            ),
            ~values={"supplier": supplierName->Option.getWithDefault("(?)")},
            (),
          ),
        ),
        (),
      )
    | Success(quantity) =>
      notifier.reset(
        Success(
          template(
            switch quantity {
            | 1 => t("1 product has been correctly added to the cart.")
            | _ => t("{{quantity}} products have been correctly added to the cart.")
            },
            ~values={"quantity": quantity->Int.toString},
            (),
          ),
        ),
        (),
      )
    | Error =>
      notifier.reset(
        Error(t("Something went wrong during the automatic order taking proccess.")),
        (),
      )
    | Loading => captureEvent(#create_automatic_order)
    | Pristine => ()
    }
    None
  }, [autoPickingStatus])

  let onRequestCartLoading = state => CartLoadingStateRequested(state)->dispatch
  let onRequestProductPickerModalOpen = opened => ProductPickerModalCalled(opened)->dispatch
  let onRequestGlobalFeeModalOpen = opened => GlobalFeeModalCalled(opened)->dispatch

  let onRequestFileImport = React.useCallback1(file =>
    switch formState.values.shopId {
    | shopId if shopId != "" => FileImported(file, shopId)->dispatch
    | _ =>
      notifier.reset(
        Error(t("Please select the shop catalog from which the CSV products will be imported.")),
        (),
      )
    }
  , [formState.values.shopId])

  let onPickProducts = React.useCallback0(products =>
    onRequestDispatch(BatchProductAdded(products))
  )

  let onPickGlobalFee = React.useCallback0(globalFee =>
    onRequestDispatch(
      switch (globalFee: OrderCartGlobalFeePickerModal.value) {
      | {globalFeeKind: FeePerUnit, amount} => ProductsFeePerUnitAllocated(amount)
      | {globalFeeKind: FeeProratedByPrice, amount} => ProductsFeeProratedByPriceAllocated(amount)
      },
    )
  )

  let onRequestSupplierProducts = React.useCallback1(() => {
    let {makeVariables, runScanEdges, rowsFromEdgesData, setDefaultOrderedQuantityValue} = module(
      OrderProductFormPickerModalTable
    )

    let fetcher = (~after) => {
      let supplier =
        supplierId !== ""
          ? Some({
              SupplierSelect.id: supplierId->Js.Nullable.return,
              name: supplierName->Option.getWithDefault(""),
            })
          : None
      apolloClient.query(
        ~query=module(OrderProductFormPickerModalTable.Query),
        ~fetchPolicy=NetworkOnly,
        makeVariables(
          ~search="",
          ~filters={shopId: Some(formState.values.shopId), ?supplier},
          ~after?,
          (),
        ),
      )
    }

    setSupplierProductsModalOpened(_ => true)
    runScanEdges(~fetcher, ())
    ->Future.mapOk(result =>
      rowsFromEdgesData(result)->Array.map(
        row =>
          CartProduct.makeProductInput(
            ~id=row.id,
            ~name=row.formattedName,
            ~description=CatalogProduct.Information.formatDescription(
              ~productKind=row.productKind,
              ~information=row.information,
              (),
            ),
            ~stockRawQuantity=?row.stockQuantity,
            ~packagingRawQuantity=?row.packaging,
            ~taxValue=row.tax,
            ~unitPrice=row.purchasedPrice,
            ~bulkPrecision=?row.bulkPrecision,
            ~capacityUnit=?row.capacityUnit,
            (),
          )->setDefaultOrderedQuantityValue,
      )
    )
    ->Future.mapOk(onPickProducts)
    ->Future.tapOk(_ => setSupplierProductsModalOpened(_ => false))
    ->ignore
  }, [supplierId])

  let columns = React.useMemo1(() => {
    let hiddenColumns = OrderEdit.isBeforeReception(~statuses)
      ? OrderCartTable.StatusBeforeReception
      : StatusAtAfterReception
    CartTable.tableColumns(
      ~hidden=hiddenColumns,
      ~statuses,
      ~editable,
      ~taxesFree=cart.taxesFree,
      ~onRequestDispatch,
    )
  }, [editable])

  let cartError = switch formState.validation {
  | Ok() => None
  | Error(errors) =>
    errors
    ->Array.keepMap(((field, error)) =>
      switch (field, error) {
      | (OrderEditForm.Schema.Field(OrderEditForm.Lenses.Cart), error) => Some(error)
      | _ => None
      }
    )
    ->Array.get(0)
  }

  let disabledIds = cart.products->Array.keepMap(product =>
    switch product {
    | Unit({identifier}) | Bulk({identifier}, _) => identifier
    }
  )

  let renderBottomActionBarEnd = () =>
    <OrderPageActions id=orderId statuses edition onRequestCancelEdition />

  <>
    <SpinnerModal title={t("Add supplier's products")} opened=supplierProductsModalOpened />
    <OrderProductFormPickerModalTable
      opened=state.productPickerModalOpened
      shopId={formState.values.shopId !== "" ? Some(formState.values.shopId) : None}
      supplierId
      ?supplierName
      disabledIds
      onCommit=onPickProducts
      onRequestClose={() => onRequestProductPickerModalOpen(false)}
    />
    <OrderCartGlobalFeePickerModal
      opened=state.globalFeeModalOpened
      onCommit=onPickGlobalFee
      onRequestClose={() => onRequestGlobalFeeModalOpen(false)}
    />
    <OrderProductFormAutoPicker
      status=autoPickingStatus
      orderId
      supplierId
      supplierName
      onPickProducts
      onQueryStatusChange={status => setAutoPickingStatus(_ => status)}
    />
    <Card variation=#unset>
      {if editable && OrderEdit.isBeforeReception(~statuses) {
        <Box spaceX=#large>
          <OrderCartActionsBar
            cartProducts=cart.products
            shopId=formState.values.shopId
            supplierId
            supplierVariantsCount
            importedCsv=state.file
            onRequestCartRowsError
            onRequestFileImport
            onRequestSupplierProducts
            onRequestCartLoading
            onRequestProductPickerModalOpen
            onRequestGlobalFeeModalOpen
            onRequestDispatch
          />
        </Box>
      } else if edition {
        <Box spaceX=#large spaceTop=#small>
          <Inline>
            <TextIconButton
              icon=#plus_light
              disabled={cart.products->Array.length <= 0}
              onPress={_ => onRequestGlobalFeeModalOpen(true)}>
              {t("order_cart.actions_bar.allocation_of_costs.text_button")->React.string}
            </TextIconButton>
          </Inline>
        </Box>
      } else {
        React.null
      }}
      {switch state.loadingState {
      | Some(loading) => <Placeholder status=Loading customText=?loading.message />
      | None =>
        <CartTable
          // FIXME - tmp solution, see below FIXME
          key={supplierId}
          columns
          data=AsyncData.Done(Ok(cart.products))
          hideCard=true
          maxHeight=540.
          erroredRowsMap=rowErrors
          placeholderEmptyState={switch (autoPickingStatus, supplierName) {
          | (Loading, Some(supplierName)) => <CartPlaceholder.Loading supplierName />
          | _ =>
            <CartPlaceholder.Default
              // FIXME - memo-like/render issue upon disabled
              disabled={supplierId === ""}
              errorMessage=?{switch (cartError, formState.submission) {
              | (Some(message), Failed(_)) => Some(t(message))
              | _ => None
              }}
              onPressCreateOrder={_ => setAutoPickingStatus(_ => Loading)}
            />
          }}
          keyExtractor=OrderCartTable.keyExtractor
          typesDrop=[#csv, #excel] // NOTE - #excel for CSV on Windows
          onSuccessDrop=?{editable ? Some(file => onRequestFileImport(file)) : None}
          onErrorDrop={message => notifier.reset(Error(message), ())}
        />
      }}
      <Box spaceLeft=#xlarge spaceRight=#xxlarge spaceBottom=#normal>
        <Inline
          align={editable && cart.products->Array.size > 0 ? #spaceBetween : #end} alignY=#top>
          {if editable && cart.products->Array.size > 0 {
            <CartOptionsView cart statuses onRequestDispatch />
          } else {
            React.null
          }}
          <CartAmountsView cart />
        </Inline>
      </Box>
    </Card>
    {if !(statuses->OrderStatus.has(#PAID)) {
      <PageBottomActionsBar displayThreshold=160. renderEnd=renderBottomActionBarEnd navOpened />
    } else {
      React.null
    }}
  </>
}

// let make = React.memoCustomCompareProps(make, (oldProps, newProps) =>
//   oldProps["cart"] === newProps["cart"] &&
//   oldProps["rowErrors"] === newProps["rowErrors"] &&
//   oldProps["statuses"] === newProps["statuses"] &&
//   oldProps["orderId"] === newProps["orderId"] &&
//   oldProps["editable"] === newProps["editable"] &&
//   oldProps["edition"] === newProps["edition"]
// )

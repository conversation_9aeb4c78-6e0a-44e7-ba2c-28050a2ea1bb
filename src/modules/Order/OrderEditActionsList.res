open Intl

type action =
  | Edition
  | DownloadPDF
  | DownloadCSV

@react.component
let make = (~id, ~statuses: array<OrderStatus.t>, ~onRequestAction) => {
  let (archivingModalOpened, setArchivingModalOpened) = React.useState(() => false)

  <>
    {switch statuses {
    | [#DRAFT] =>
      <Menu>
        <MenuItem
          content=Text(t("Download PDF")) action=Callback(() => onRequestAction(DownloadPDF))
        />
        <MenuItem
          content=Text(t("Download CSV")) action=Callback(() => onRequestAction(DownloadCSV))
        />
        <MenuItem
          content=Text(t("Archive")) action=Callback(() => setArchivingModalOpened(_ => true))
        />
      </Menu>
    | [#FINALIZED]
    | [#ACCEPTED, #NOT_RECEIVED] =>
      <Menu>
        <MenuItem content=Text(t("Edit order")) action=Callback(() => onRequestAction(Edition)) />
        <MenuItem
          content=Text(t("Download PDF")) action=Callback(() => onRequestAction(DownloadPDF))
        />
        <MenuItem
          content=Text(t("Download CSV")) action=Callback(() => onRequestAction(DownloadCSV))
        />
        <MenuItem
          content=Text(t("Archive")) action=Callback(() => setArchivingModalOpened(_ => true))
        />
      </Menu>
    | [#RECEIVING] =>
      <Menu>
        <MenuItem
          content=Text(t("Continue reception")) action=Callback(() => onRequestAction(Edition))
        />
        <MenuItem
          content=Text(t("Download PDF")) action=Callback(() => onRequestAction(DownloadPDF))
        />
        <MenuItem
          content=Text(t("Download CSV")) action=Callback(() => onRequestAction(DownloadCSV))
        />
        <MenuItem
          content=Text(t("Archive")) action=Callback(() => setArchivingModalOpened(_ => true))
        />
      </Menu>
    | [#RECEIVED, #TO_PAY] =>
      <Menu>
        <MenuItem
          content=Text(t("Edit order prices")) action=Callback(() => onRequestAction(Edition))
        />
        <MenuItem
          content=Text(t("Download PDF")) action=Callback(() => onRequestAction(DownloadPDF))
        />
        <MenuItem
          content=Text(t("Download CSV")) action=Callback(() => onRequestAction(DownloadCSV))
        />
        <MenuItem
          content=Text(t("Archive")) action=Callback(() => setArchivingModalOpened(_ => true))
        />
      </Menu>
    | [#RECEIVED, #PAID] =>
      <Menu>
        <MenuItem
          content=Text(t("Download PDF")) action=Callback(() => onRequestAction(DownloadPDF))
        />
        <MenuItem
          content=Text(t("Download CSV")) action=Callback(() => onRequestAction(DownloadCSV))
        />
        <MenuItem
          content=Text(t("Archive")) action=Callback(() => setArchivingModalOpened(_ => true))
        />
      </Menu>
    | [#ARCHIVED] =>
      <Menu>
        <MenuItem
          content=Text(t("Download PDF")) action=Callback(() => onRequestAction(DownloadPDF))
        />
        <MenuItem
          content=Text(t("Download CSV")) action=Callback(() => onRequestAction(DownloadCSV))
        />
      </Menu>
    | _ => React.null
    }}
    <OrderArchiveModal
      orderId=id
      opened=archivingModalOpened
      onRequestClose={() => setArchivingModalOpened(_ => false)}
    />
  </>
}

// let make = React.memoCustomCompareProps(make, (oldProps, newProps) =>
//   oldProps["id"] === newProps["id"] && oldProps["statuses"] === newProps["statuses"]
// )

module SupplierIdMutation = %graphql(`
  mutation updateVariant_supplier($id: ID!, $input: InputUpdateVariant!) {
    updateVariant(id: $id, input: $input) {
      id
      supplier { id }
    }
  }
`)

@react.component
let make = (~supplierId, ~supplierName, ~variantId, ~shopId, ~onDismiss=?) => {
  let supplierId = supplierId->Js.Nullable.return
  let mutate = SupplierIdMutation.use()->fst

  let onMutateSupplierRequested = React.useCallback0(supplier => {
    let input = SupplierIdMutation.makeInputObjectInputUpdateVariant(
      ~supplierId=switch supplier->Option.flatMap(supplier =>
        supplier.SupplierSelect.id->Js.Nullable.toOption
      ) {
      | Some(supplierId) => supplierId
      | None => %raw(`null`)
      },
      (),
    )

    mutate(SupplierIdMutation.makeVariables(~id=variantId, ~input, ()))
    ->ApolloHelpers.mutationPromiseToFutureResult
    ->Future.mapOk(_ => Some(variantId))
    ->ignore
  })

  <SupplierSelect
    preset=#inputField({OverlayTriggerView.required: false})
    shopId=Some(shopId)
    defaultOpen=true
    value=Some({SupplierSelect.id: supplierId, name: supplierName})
    onChange=onMutateSupplierRequested
    ?onDismiss
  />
}

// let make = React.memoCustomCompareProps(make, (oldProps, newProps) =>
//   oldProps["supplierId"] === newProps["supplierId"] &&
//   oldProps["variantId"] === newProps["variantId"] &&
//   oldProps["shopId"] === newProps["shopId"]
// )

open Intl

module CatalogVariantLossReportFormLenses = %lenses(
  type state = {
    shopId: option<string>,
    deviceId: option<string>,
    variantId: option<string>,
    variantCapacityUnit: option<string>,
    variantCapacityPrecision: option<int>,
    reason: StockActivityReason.t,
    quantity: float,
    comment: string,
  }
)

module CatalogVariantLossReportForm = Form.Make(CatalogVariantLossReportFormLenses)

module CreateStockActivity = %graphql(`
  mutation CreateStockActivity($input: InputCreateStockActivity!) {
    createStockActivity(input: $input) {
      id
    }
  }
`)

let useLossReportMutation = (~cku) => {
  let (mutate, result) = CreateStockActivity.use()
  let captureEvent = SessionTracker.useCaptureEvent()

  React.useCallback((_, state) => {
    switch state {
    | {
        CatalogVariantLossReportFormLenses.variantId: Some(variantId),
        shopId: Some(shopId),
        deviceId: Some(deviceId),
        reason,
        variantCapacityUnit: capacityUnit,
        variantCapacityPrecision: capacityPrecision,
        comment,
        quantity,
      } =>
      let input = CreateStockActivity.makeInputObjectInputCreateStockActivity(
        ~kind=#LOSS,
        ~reason,
        ~variantId,
        ~capacityUnit?,
        ~capacityPrecision?,
        ~quantity=quantity->StockActivityQuantity.toRawValue(~capacityPrecision?),
        ~comment=?comment->String.length > 0 ? Some(comment) : None,
        ~shopId,
        ~deviceId,
        (),
      )

      mutate(CreateStockActivity.makeVariables(~input, ()))
      ->ApolloHelpers.mutationPromiseToFutureResult
      ->Future.mapOk(_ => Some(cku))
      ->Future.tapOk(_ => {
        captureEvent(#catalog_variant_page_report_modal_click_save)
        switch result.client {
        | Some({resetStore}) => ignore(resetStore())
        | _ => ()
        }
      })
    | {variantId: None} | {shopId: None} | {deviceId: None} =>
      Future.value(Error(t("Something went wrong")))
    }
  })
}

// TODO - remove this dep to improve module isolation
module Config = CatalogVariant__Config

let defaultFormValues = {
  CatalogVariantLossReportFormLenses.shopId: None,
  deviceId: None,
  variantId: None,
  variantCapacityUnit: None,
  variantCapacityPrecision: None,
  reason: #LOSS,
  quantity: 0.,
  comment: "",
}

let makeFormValues = (~shopId=?, ~shopsVariantStock, ~shops, ()) => {
  let maybeVariantStock = shopsVariantStock->Array.getBy(shopVariantStock => {
    switch shopId {
    | Some(shopId) => shopId === shopVariantStock.Config.shopId
    | None => false
    }
  })
  switch maybeVariantStock {
  | Some({Config.capacityUnit: capacityUnit, capacityPrecision, shopId, variantId}) =>
    let maybeShop = shops->Array.getBy(shop => shop.Auth__Types.id === shopId)
    switch maybeShop {
    | Some({activeWebDeviceId: deviceId}) => {
        ...defaultFormValues,
        shopId: Some(shopId),
        deviceId: Some(deviceId),
        variantId: Some(variantId),
        variantCapacityUnit: capacityUnit,
        variantCapacityPrecision: capacityPrecision,
      }
    | _ => defaultFormValues
    }
  | None => defaultFormValues
  }
}

let schema = [
  CatalogVariantLossReportForm.Schema.Custom(
    ShopId,
    ({shopId}) =>
      switch shopId {
      | Some(_) => Ok()
      | None => Error(t("Missing shop"))
      },
  ),
  Custom(
    Quantity,
    ({quantity, variantCapacityPrecision}) =>
      switch variantCapacityPrecision {
      | Some(_) if quantity > 0. => Ok()
      | None if quantity >= 1. => Ok()
      | _ => Error(t("This value must be positive"))
      },
  ),
]

@react.component
let make = (~cku, ~opened, ~shopsVariantStock, ~onRequestClose) => {
  let shops = Auth.useShops()
  let activeShop = Auth.useActiveShop()

  let onSubmit = useLossReportMutation(~cku)

  let formInitialShopId = activeShop->Option.map(shop => shop.id)
  let formId = cku ++ formInitialShopId->Option.getWithDefault("")
  let formInitialValues = React.useMemo3(
    () => makeFormValues(~shopId=?formInitialShopId, ~shopsVariantStock, ~shops, ()),
    (formInitialShopId, shopsVariantStock, shops),
  )

  let onSubmitSuccess = _ => onRequestClose()

  let (formState, formDispatch) = CatalogVariantLossReportForm.useFormPropState({
    id: formId,
    initialValues: formInitialValues,
    onSubmitSuccess,
    schema,
    resetValuesAfterSubmission: true,
  })

  let formValuesShopId = formState.values.shopId

  React.useEffect1(() => {
    let formValues = makeFormValues(~shopId=?formValuesShopId, ~shopsVariantStock, ~shops, ())
    formDispatch(
      FieldsValuesChanged(
        values => {
          ...values,
          deviceId: formValues.deviceId,
          variantId: formValues.variantId,
          variantCapacityPrecision: formValues.variantCapacityPrecision,
          variantCapacityUnit: formValues.variantCapacityUnit,
        },
      ),
    )
    None
  }, [formValuesShopId])

  <Modal title={t("Report a loss")} hideFooter=true opened onRequestClose>
    <CatalogVariantLossReportForm.FormProvider propState=(formState, formDispatch)>
      <Box spaceTop=#xlarge spaceX=#xlarge>
        <Stack space=#normal>
          {if activeShop->Option.isNone {
            <CatalogVariantLossReportForm.InputSelect
              field=ShopId
              label={t("Shop")}
              placeholder={t("Select a shop")}
              sections={[
                {
                  Select.items: shopsVariantStock->Array.map(({shopId, shopName}) => {
                    {
                      Select.key: shopId,
                      label: shopName,
                      value: Some(shopId),
                    }
                  }),
                },
              ]}
            />
          } else {
            React.null
          }}
          <CatalogVariantLossReportForm.InputSelect
            field=Reason
            label={t("Loss reason")}
            sections={
              let items = StockActivityReason.values->Array.map(reason => {
                Select.key: reason->StockActivityReason.toString,
                label: reason->StockActivityReason.toLabel,
                value: reason,
              })
              [{Select.items: items}]
            }
          />
          <CatalogVariantLossReportForm.InputNumber
            field=Quantity
            label={t("Quantity")}
            appender=?{switch formState.values.variantCapacityUnit {
            | Some(unit) => Some(InputNumberField.Custom(unit))
            | _ => None
            }}
            minValue=0.
            precision={formState.values.variantCapacityPrecision->Option.getWithDefault(0)}
          />
          <CatalogVariantLossReportForm.InputTextArea field=Comment label={t("Comment")} />
        </Stack>
        <Box spaceY=#xlarge>
          <Inline align=#end space=#xmedium>
            <Button size=#medium variation=#neutral onPress={_ => onRequestClose()}>
              {t("Cancel")->React.string}
            </Button>
            <CatalogVariantLossReportForm.SubmitButton
              text={t("Save")} variation=#success size=#medium onSubmit
            />
          </Inline>
        </Box>
      </Box>
    </CatalogVariantLossReportForm.FormProvider>
  </Modal>
}

// let make = React.memoCustomCompareProps(make, (oldProps, newProps) =>
//   oldProps["cku"] === newProps["cku"] &&
//   oldProps["shopsVariantStock"] === newProps["shopsVariantStock"] &&
//   oldProps["opened"] === newProps["opened"]
// )

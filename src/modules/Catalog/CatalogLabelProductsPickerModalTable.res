open Intl

module Query = %graphql(`
  query VariantsQuery($search: String, $filterBy: InputVariantsQueryFilter, $after: String, $first: Int) {
    variants(search: $search, filterBy: $filterBy, after: $after, first: $first) {
      pageInfo {
        endCursor
        hasNextPage
      }
      totalCount
      edges {
        node {
          id
          createdAt
          formattedName
          name
          product {
            id
            name
            kind @ppxOmitFutureValue
            color @ppxOmitFutureValue
            wineType @ppxOmitFutureValue
            whiteWineType @ppxOmitFutureValue
            beerType
            producer
            designation
            family
            region
            country
            tax { value }
          }
          stockKeepingUnit
          priceLookUpCode
          internalCode
          supplier { companyName }
          formattedCategory
          alcoholVolume
          variantPrices(first:50) {
            edges {
              node {
                valueIncludingTax
                fromQuantity
                price { id }
              }
            }
          }
        }
      }
    }
  }
`)

type filters = {
  shopId: option<string>,
  category?: CatalogCategorySelectFilter.parentCategory,
  supplier?: SupplierSelect.supplier,
  producer?: string,
}

type row = {
  id: string,
  createdAt: Js.Date.t,
  formattedName: string,
  productKind: CatalogProduct.Kind.t,
  information: CatalogProduct.Information.t,
  hasValidVariantPrice: bool,
}

let rowsFromEdgesData = (data, ~priceId) =>
  data->Array.map(edge => {
    let hasValidVariantPrice =
      edge.Query.node.variantPrices.edges
      ->Array.getBy(({node}) =>
        switch node {
        | {price: Some({id}), valueIncludingTax, fromQuantity: Some(1.) | None} =>
          id === priceId->Uuid.toString && valueIncludingTax > 0.
        | _ => false
        }
      )
      ->Option.isSome

    {
      id: edge.node.id,
      createdAt: edge.node.createdAt,
      formattedName: edge.node.formattedName,
      productKind: edge.node.product.kind,
      information: {
        productName: edge.node.product.name,
        variantName: edge.node.name,
        sku: ?edge.node.stockKeepingUnit,
        plu: ?edge.node.priceLookUpCode->Option.map(Int.toString),
        internalCode: ?edge.node.internalCode,
        color: ?edge.node.product.color,
        producerName: ?edge.node.product.producer,
        designation: ?edge.node.product.designation,
        productFamily: ?edge.node.product.family,
        wineType: ?edge.node.product.wineType,
        whiteWineType: ?edge.node.product.whiteWineType,
        beerType: ?edge.node.product.beerType,
        region: ?edge.node.product.region,
        country: edge.node.product.country->Option.getWithDefault("—"),
        categoryName: edge.node.formattedCategory->Option.getWithDefault("—"),
        supplierName: ?edge.node.supplier->Option.map(supplier => supplier.companyName),
        alcoholVolume: ?edge.node.alcoholVolume->Option.map(alcoholVolume =>
          alcoholVolume->Intl.decimalFormat(~maximumFractionDigits=1) ++ "°"
        ),
      },
      hasValidVariantPrice,
    }
  })

let columns = [
  {
    Table.key: "product",
    name: t("Product"),
    render: ({data, disabled}) => {
      let hoursThreshold = 24
      let badgeNew =
        DateHelpers.diffInHours(Js.Date.make(), data.createdAt) <= hoursThreshold
          ? Some({
              ProductReferenceTableCell.variation: #information,
              text: t("New"),
            })
          : None

      <ProductReferenceTableCell
        disabled
        badge=?badgeNew
        productKind=data.productKind
        information=data.information
        errorMessage=?{switch (disabled, data.hasValidVariantPrice) {
        | (true, false) => Some(t("No retail prices found with the selected price list"))
        | _ => None
        }}
      />
    },
  },
]

let setDefaultOrderedQuantityValue: Accounting.Types.productInput => Accounting.Types.productInput = productInput =>
  switch productInput {
  | Unit({product}) =>
    Unit({
      product: {
        ...product,
        quantity: 1,
      },
    })
  | Bulk({product, precision}) =>
    Bulk({
      product: {
        ...product,
        quantity: 1.,
      },
      precision,
    })
  }

module PickerModal = {
  @react.component
  let make = React.memo((~children, ~opened, ~selectedProducts, ~onCommit, ~onRequestClose) => {
    let renderStartText = () => {
      let length = selectedProducts->Array.length
      let shouldBePrimaryView = length > 0
      let formattedText =
        `${length->Int.toString} ` ++ t(isPlural(length) ? "selected products" : "selected product")

      <Inline>
        <TextStyle
          weight={shouldBePrimaryView ? #strong : #regular}
          variation={shouldBePrimaryView ? #primary : #neutral}>
          {formattedText->React.string}
        </TextStyle>
      </Inline>
    }

    let onCommit = () => onCommit(selectedProducts)

    <Modal
      title={t("Product append")}
      variation=#secondary
      compact=false
      allowCommit={selectedProducts->Array.length > 0}
      backgroundColor=Colors.neutralColor00
      renderStartText
      abortButtonText={t("Cancel")}
      commitButtonText={t("Add products")}
      commitButtonVariation=#primary
      commitButtonCallback=onCommit
      opened
      onRequestClose>
      children
    </Modal>
  })
}

module Reducer = {
  type state = {
    searchQuery: string,
    filters: filters,
    selectedProducts: array<row>,
  }

  type action =
    | Searched(string)
    | FiltersUpdated(filters => filters)
    | ProductSelectionUpdated(Table.selection, array<row>)

  let initialState = (~filters) => {
    selectedProducts: [],
    searchQuery: "",
    filters,
  }

  let make = (state, action) =>
    switch action {
    | Searched(searchQuery) => {...state, searchQuery}
    | FiltersUpdated(updateStateFilters) => {...state, filters: updateStateFilters(state.filters)}
    | ProductSelectionUpdated(tableSelection, allProducts) =>
      let prev = state.selectedProducts
      let selectedProducts = switch tableSelection {
      | Selected(keys) =>
        keys->Array.keepMap(selectedRowId =>
          allProducts->Array.concat(prev)->Array.getBy(row => row.id === selectedRowId)
        )
      | All => prev
      }
      {...state, selectedProducts}
    }
}

let makeVariables = (~search, ~filters, ~after=?, ()) =>
  Query.makeVariables(
    ~first=20,
    ~after?,
    ~search,
    ~filterBy=Query.makeInputObjectInputVariantsQueryFilter(
      ~shopIds=?switch filters.shopId {
      | Some(id) => Some({_in: [id]})
      | None => None
      },
      ~supplierId=?switch filters.supplier {
      | Some(supplier) =>
        Some(
          Query.makeInputObjectNullableStringEqualsFilter(
            ~_equals=switch supplier.id->Js.Nullable.toOption {
            | Some(supplierId) => supplierId
            | None => %raw(`null`)
            },
            (),
          ),
        )
      | None => None
      },
      ~categoryId=?switch filters.category {
      | Some(category) =>
        Some(
          Query.makeInputObjectNullableStringEqualsFilter(
            ~_equals=switch category.id->Js.Nullable.toOption {
            | Some(categoryId) => categoryId
            | None => %raw(`null`)
            },
            (),
          ),
        )
      | None => None
      },
      ~producer=?switch filters.producer {
      | Some(producer) => Some(Query.makeInputObjectStringEqualsFilter(~_equals=producer, ()))
      | None => None
      },
      ~active=Query.makeInputObjectBooleanEqualsFilter(~_equals=true, ()),
      (),
    ),
    (),
  )

@react.component
let make = (~opened, ~shopId, ~priceId, ~disabledIds=[], ~onCommit, ~onRequestClose) => {
  let (state, dispatch) = React.useReducer(
    Reducer.make,
    Reducer.initialState(~filters={shopId: shopId}),
  )
  let query = Query.use(
    makeVariables(~search=state.searchQuery, ~filters=state.filters, ()),
    ~skip=shopId->Option.isNone,
    ~notifyOnNetworkStatusChange=true,
    ~fetchPolicy=NetworkOnly,
    ~nextFetchPolicy=CacheFirst,
  )

  let asyncResult =
    query->ApolloHelpers.useQueryResultToAsyncResultWithVariablesReloading2(
      state.searchQuery,
      shopId,
    )
  let data =
    asyncResult->AsyncResult.mapOk(data => rowsFromEdgesData(data.variants.edges, ~priceId))

  React.useEffect1(() => {
    if !opened && state.searchQuery !== "" {
      dispatch(Searched(""))
    }
    None
  }, [opened])

  let onLoadMore = React.useCallback1(() =>
    switch asyncResult {
    | Done(Ok(data)) if data.variants.pageInfo.hasNextPage === Some(true) =>
      query.fetchMore(
        ~variables=makeVariables(
          ~search=state.searchQuery,
          ~filters=state.filters,
          ~after=?data.variants.pageInfo.endCursor,
          (),
        ),
        ~updateQuery=(prevResult, {fetchMoreResult}) =>
          switch fetchMoreResult {
          | Some({variants: newVariants}) => {
              variants: {
                ...newVariants,
                edges: prevResult.variants.edges->Array.concat(newVariants.edges),
              },
            }
          | None => prevResult
          },
        (),
      )->ignore
    | _ => ()
    }
  , [asyncResult])

  let onSelectChange = React.useCallback2(selection => {
    let rowsData = switch data {
    | Done(Ok(data)) | Reloading(Ok(data)) => data
    | _ => []
    }
    dispatch(ProductSelectionUpdated(selection, rowsData))
  }, (data, disabledIds))

  let disabledRowsKeys = switch data {
  | Done(Ok(rows)) =>
    disabledIds->Array.concat(
      rows->Array.keepMap(row => !row.hasValidVariantPrice ? Some(row.id) : None),
    )
  | _ => disabledIds
  }

  let filters = [
    <SupplierSelect
      label={t("Supplier")}
      preset=#filter
      size=#compact
      showDefaultItem=true
      hideOverlayFooter=true
      shopId
      value={state.filters.supplier}
      onChange={supplier => dispatch(FiltersUpdated(prev => {...prev, ?supplier}))}
    />,
    <CatalogCategorySelectFilter
      shopId
      value={state.filters.category}
      onChange={category => dispatch(FiltersUpdated(prev => {...prev, ?category}))}
    />,
    <CatalogProducerSelect
      shopId
      value={state.filters.producer}
      onChange={producer => dispatch(FiltersUpdated(prev => {...prev, ?producer}))}
    />,
  ]

  <PickerModal opened selectedProducts=state.selectedProducts onCommit onRequestClose>
    <AnimatedRender displayed=opened animation=#fadePopinTranslation duration=300>
      <div style={ReactDOMStyle.make(~display="flex", ~minHeight="70vh", ~maxHeight="70vh", ())}>
        <TableView
          searchPlaceholder={t("Search product")}
          columns
          data
          keyExtractor={row => row.id}
          disabledRowsKeys
          selectionEnabled=true
          selectAllEnabled=false
          hideReloadingPlaceholder=true
          compactRows=true
          filters
          onSearchQueryChange={value => dispatch(Searched(value))}
          onSelectChange
          onLoadMore
        />
      </div>
    </AnimatedRender>
  </PickerModal>
}

// let make = React.memoCustomCompareProps(make, (oldProps, newProps) =>
//   oldProps["opened"] === newProps["opened"]
// )

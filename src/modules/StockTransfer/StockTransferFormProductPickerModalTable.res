open Intl

module Query = %graphql(`
  query VariantsQuery($search: String, $filterBy: InputVariantsDistinctOnCkuQueryFilter, $after: String, $first: Int) {
    variantsDistinctOnCku(search: $search, filterBy: $filterBy, after: $after, first: $first) {
      pageInfo {
        startCursor
        endCursor
        hasNextPage
      }
      edges {
        node {
          id
          cku
          createdAt
          name
          formattedName
          product {
            id
            name
            kind @ppxOmitFutureValue
            color @ppxOmitFutureValue
            wineType @ppxOmitFutureValue
            whiteWineType @ppxOmitFutureValue
            beerType
            producer
            designation
            family
            region
            country
            tax { value }
          }
          stockKeepingUnit
          internalCode
          priceLookUpCode
          supplier { companyName }
          formattedCategory
          alcoholVolume
          formattedPurchasedPrice
          bulk
          stock {
            rawQuantity
            formattedQuantity
            state @ppxOmitFutureValue
          }
        }
      }
      totalCount
    }
  }
`)

type row = {
  id: string,
  cku: string,
  createdAt: Js.Date.t,
  formattedName: string,
  productKind: CatalogProduct.Kind.t,
  information: CatalogProduct.Information.t,
  purchasePrice: option<string>,
  stockRawQuantity: int,
  stockFormattedQuantity: string,
  stockState: option<Product.Stock.t>,
  bulk: bool,
}

let rowsFromEdgesData = data =>
  data->Array.map(edge => {
    id: edge.Query.node.id,
    cku: edge.node.cku,
    createdAt: edge.node.createdAt,
    formattedName: edge.node.formattedName,
    productKind: edge.node.product.kind,
    information: {
      productName: edge.node.product.name,
      variantName: edge.node.name,
      sku: ?edge.node.stockKeepingUnit,
      plu: ?edge.node.priceLookUpCode->Option.map(Int.toString),
      internalCode: ?edge.node.internalCode,
      color: ?edge.node.product.color,
      producerName: ?edge.node.product.producer,
      designation: ?edge.node.product.designation,
      productFamily: ?edge.node.product.family,
      wineType: ?edge.node.product.wineType,
      whiteWineType: ?edge.node.product.whiteWineType,
      beerType: ?edge.node.product.beerType,
      region: ?edge.node.product.region,
      country: edge.node.product.country->Option.getWithDefault("—"),
      categoryName: edge.node.formattedCategory->Option.getWithDefault("—"),
      supplierName: ?edge.node.supplier->Option.map(supplier => supplier.companyName),
      alcoholVolume: ?edge.node.alcoholVolume->Option.map(alcoholVolume =>
        alcoholVolume->Intl.decimalFormat(~maximumFractionDigits=1) ++ "°"
      ),
    },
    bulk: edge.node.bulk->Option.getWithDefault(false),
    purchasePrice: edge.node.formattedPurchasedPrice,
    stockRawQuantity: edge.node.stock.rawQuantity->Option.getWithDefault(0),
    stockFormattedQuantity: edge.node.stock.formattedQuantity->Option.getWithDefault("—"),
    stockState: edge.node.stock.state,
  })

let columns = [
  {
    Table.key: "reference",
    name: t("Product"),
    layout: {width: 65.->#pct},
    render: ({data: product, disabled}) => {
      let badgeNew = {
        let hoursThreshold = 24
        DateHelpers.diffInHours(Js.Date.make(), product.createdAt) <= hoursThreshold
          ? Some({
              ProductReferenceTableCell.variation: #information,
              text: t("New"),
            })
          : None
      }
      <ProductReferenceTableCell
        disabled badge=?badgeNew productKind=product.productKind information=product.information
      />
    },
  },
  {
    key: "stock",
    name: t("Stock exp."),
    render: ({data: product, disabled}) =>
      if !disabled {
        <ProductStockTableCell
          value=Some(product.stockFormattedQuantity) state=?product.stockState size=#xsmall
        />
      } else {
        <TextStyle variation=#subdued size=#small> {t("Already added")->React.string} </TextStyle>
      },
  },
  {
    key: "purchasedPrice",
    name: t("Purchase price"),
    layout: {width: 120.->#px},
    render: ({data: product, disabled}) =>
      if !disabled {
        <ProductPriceTableCell size=#xsmall value=product.purchasePrice />
      } else {
        React.null
      },
  },
]

module PickerModal = {
  let setTransferredQuantityValue = product => {
    ...product,
    StockTransferFormProducts.transferredQuantity: 1,
  }

  @react.component
  let make = React.memo((~children, ~opened, ~selectedProducts, ~onCommit, ~onRequestClose) => {
    let renderStartText = () => {
      let length = selectedProducts->Array.length
      let shouldBePrimaryView = length > 0
      let formattedText =
        `${length->Int.toString} ` ++ t(isPlural(length) ? "selected products" : "selected product")

      <Inline>
        <TextStyle
          weight={shouldBePrimaryView ? #strong : #regular}
          variation={shouldBePrimaryView ? #primary : #neutral}>
          {formattedText->React.string}
        </TextStyle>
      </Inline>
    }

    let onCommit = () =>
      onCommit(
        selectedProducts
        ->Array.keep(row => !row.bulk) // TODO
        ->Array.map(row =>
          {
            senderVariantId: row.id,
            recipientVariantId: "",
            cku: row.cku,
            name: row.formattedName,
            description: CatalogProduct.Information.formatDescription(
              ~productKind=row.productKind,
              ~information=row.information,
              (),
            ),
            stockKeepingUnit: row.information.sku,
            purchasePrice: row.purchasePrice,
            senderStockQuantity: row.stockRawQuantity,
            senderStockState: row.stockState,
            recipientStockQuantity: None,
            recipientStockState: None,
            transferredQuantity: 0,
          }->setTransferredQuantityValue
        ),
      )

    <Modal
      title={t("Product append")}
      variation=#secondary
      compact=false
      allowCommit={selectedProducts->Array.length > 0}
      backgroundColor=Colors.neutralColor00
      renderStartText
      abortButtonText={t("Cancel")}
      commitButtonText={t("Add products")}
      commitButtonVariation=#primary
      commitButtonCallback=onCommit
      opened
      onRequestClose>
      children
    </Modal>
  })
}

let makeVariables = (~search, ~shopId, ~after=?, ()) =>
  Query.makeVariables(
    ~first=20,
    ~after?,
    ~search,
    ~filterBy=Query.makeInputObjectInputVariantsDistinctOnCkuQueryFilter(
      ~shopIds={_in: [shopId]},
      (),
    ),
    (),
  )

@react.component
let make = (
  ~opened,
  ~senderShopId,
  ~recipientShopId,
  ~disabledIds=[],
  ~onCommit,
  ~onRequestClose,
) => {
  let (selectedProducts, setSelectedProducts) = React.useState(() => [])
  let (searchQuery, setSearchQuery) = React.useState(() => "")

  let shopId = senderShopId
  let query = Query.use(
    makeVariables(~search=searchQuery, ~shopId, ()),
    ~skip=senderShopId === "" || recipientShopId === "",
    ~notifyOnNetworkStatusChange=true,
    ~fetchPolicy=NetworkOnly,
    ~nextFetchPolicy=CacheFirst,
  )

  let asyncResult =
    query->ApolloHelpers.useQueryResultToAsyncResultWithVariablesReloading2(searchQuery, shopId)
  let data =
    asyncResult->AsyncResult.mapOk(data => rowsFromEdgesData(data.variantsDistinctOnCku.edges))

  React.useEffect1(() => {
    if !opened && searchQuery !== "" {
      setSearchQuery(_ => "")
    }
    None
  }, [opened])

  let onLoadMore = React.useCallback1(() =>
    switch asyncResult {
    | Done(Ok(data)) if data.variantsDistinctOnCku.pageInfo.hasNextPage === Some(true) =>
      query.fetchMore(
        ~variables=makeVariables(
          ~search=searchQuery,
          ~shopId,
          ~after=?data.variantsDistinctOnCku.pageInfo.endCursor,
          (),
        ),
        ~updateQuery=(prevResult, {fetchMoreResult}) =>
          switch fetchMoreResult {
          | Some({variantsDistinctOnCku: newVariants}) => {
              variantsDistinctOnCku: {
                ...newVariants,
                edges: prevResult.variantsDistinctOnCku.edges->Array.concat(newVariants.edges),
              },
            }
          | None => prevResult
          },
        (),
      )->ignore
    | _ => ()
    }
  , [asyncResult])

  let onSelectChange = React.useCallback2(selection =>
    setSelectedProducts(prev => {
      let rowsData = switch data {
      | Done(Ok(data)) | Reloading(Ok(data)) => data
      | _ => []
      }
      switch selection {
      | Table.Selected(keys) =>
        keys->Array.keepMap(
          selectedRowId =>
            rowsData->Array.concat(prev)->Array.getBy(row => row.id === selectedRowId),
        )
      | All => prev // NOTE - not supported
      }
    })
  , (data, disabledIds))

  let placeholderEmptyState =
    <Placeholder
      status={senderShopId !== "" && recipientShopId !== ""
        ? NoDataAvailable
        : Pending({
            illustration: Some(Illustration.shopMissing),
            title: t("Beware !"),
            message: t("Please select the shops linked to the transfer to start adding products."),
          })}
    />

  <PickerModal opened selectedProducts onCommit onRequestClose>
    <AnimatedRender displayed=opened animation=#fadePopinTranslation duration=300>
      <div style={ReactDOMStyle.make(~display="flex", ~minHeight="70vh", ~maxHeight="70vh", ())}>
        <TableView
          searchPlaceholder={t("Search product")}
          columns
          data
          keyExtractor={row => row.id}
          disabledRowsKeys=disabledIds
          selectionEnabled=true
          selectAllEnabled=false
          hideReloadingPlaceholder=true
          compactRows=true
          placeholderEmptyState
          onSearchQueryChange={value => setSearchQuery(_ => value)}
          onSelectChange
          onLoadMore
        />
      </div>
    </AnimatedRender>
  </PickerModal>
}

// let make = React.memoCustomCompareProps(make, (oldProps, newProps) =>
//   oldProps["opened"] === newProps["opened"]
// )

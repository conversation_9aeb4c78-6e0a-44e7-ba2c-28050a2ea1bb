open Intl
open Accounting.Types

module Utils = Accounting__Utils
module InnerUtils = CartProductFeeFieldset__Utils

@react.component
let make = (
  ~displayLabel,
  ~fee: fee,
  ~product,
  ~onRequestProductFeeUpdate,
  ~onRequestProductFeeReplicate,
  ~onRequestProductFeeRemove,
) => {
  let feeRef = React.useRef(fee)

  React.useEffect1(() => {
    feeRef.current = fee
    None
  }, [fee])

  let onChangeKind = kind => {
    let fee = feeRef.current
    onRequestProductFeeUpdate(fee.id, {...fee, kind})
  }

  let onChangeAmount = amount => {
    let fee = feeRef.current
    onRequestProductFeeUpdate(fee.id, {...fee, amount})
  }

  let inputAppender = switch product {
  | Bulk({capacityUnit: Some(unit)}, _) =>
    InputNumberField.Custom(#EUR->Intl.toCurrencySymbol ++ `/${unit}`)
  | _ => Currency(#EUR)
  }

  let costTypeSections = {
    open InnerUtils

    let defaultItem = {
      Select.key: fee.kind->optionToText,
      label: fee.kind->optionToText,
      value: fee.kind,
    }
    let items =
      product
      ->Utils.getAvailableFeeKinds
      ->Array.map(feedKind => {
        Select.key: feedKind->optionToText,
        label: feedKind->optionToText,
        value: feedKind,
      })

    [{Select.items: [defaultItem]->Array.concat(items)}]
  }

  <Group grid=["1fr", "1fr", "auto"] spaceX=#medium alignY={#"flex-end"}>
    <Select
      preset=#inputField({required: false})
      label=?{displayLabel ? Some(t("Cost type")) : None}
      sections=costTypeSections
      value=fee.kind
      onChange=onChangeKind
    />
    <InputNumberField
      label=?{displayLabel ? Some(t("Unit amount")) : None}
      appender=inputAppender
      minValue=0.
      minPrecision=3
      precision=5
      value=fee.amount
      onChange=onChangeAmount
    />
    <Menu variation=#more_round>
      <MenuItem
        content={Text(t("Apply to all products"))}
        action=Callback(() => onRequestProductFeeReplicate(fee))
      />
      <MenuItem
        content={Text(t("Remove"))} action=Callback(() => onRequestProductFeeRemove(fee.id))
      />
    </Menu>
  </Group>
}

// let make = React.memoCustomCompareProps(make, (oldProps, newProps) =>
//   oldProps["fee"] == newProps["fee"]
// )

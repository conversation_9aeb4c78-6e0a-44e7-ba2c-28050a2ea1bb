open Intl
open StyleX

let styles = StyleX.create({
  "wrapper": style(~flex="1", ~display=#flex, ~flexDirection=#column, ~alignItems=#"flex-end", ()),
  "row": style(~display=#flex, ~alignItems=#center, ~paddingTop="3px", ~paddingBottom="1px", ()),
  "subrow": style(
    ~position=#relative,
    ~display=#flex,
    ~alignItems=#center,
    ~left="2px",
    ~paddingTop="3px",
    ~paddingBottom="5px",
    (),
  ),
  "label": style(~minWidth="194px", ~lineHeight="1", ()),
  "amount": style(
    ~display=#flex,
    ~flexDirection=#column,
    ~alignItems=#"flex-start",
    ~minWidth="100px",
    (),
  ),
})

let bigToFloat = Accounting.Formatter.Utils.bigToFloat
let currencyFormat = value =>
  value->Intl.currencyFormat(~currency=#EUR, ~minimumFractionDigits=3, ~maximumFractionDigits=3)

@react.component
let make = (~cart: Accounting.Types.cart) => {
  let formattedTotalAmountExcludingTaxes =
    bigToFloat(cart.totalAmountExcludingTaxes->Option.getExn)->currencyFormat
  let formattedTotalAmountExcludingGlobalDiscounts =
    bigToFloat(cart.totalAmountExcludingGlobalDiscounts->Option.getExn)->currencyFormat
  let formattedTotalAmountOfGoods =
    bigToFloat(cart.totalAmountOfGoods->Option.getExn)->currencyFormat
  let formattedTotalAmountIncludingTaxes =
    bigToFloat(cart.totalAmountIncludingTaxes->Option.getExn)->currencyFormat

  <DivX style={StyleX.props([styles["wrapper"]])}>
    {switch cart.discounts[0] {
    | Some(discount) if discount.value > 0. =>
      let formattedDiscountAmount = bigToFloat(discount.amount->Option.getExn)->currencyFormat

      <Box spaceBottom=#small>
        <DivX style={StyleX.props([styles["row"]])}>
          <DivX style={StyleX.props([styles["label"]])}>
            <TextStyle size=#large> {t("Sub total excl. VAT")->React.string} </TextStyle>
          </DivX>
          <DivX style={StyleX.props([styles["amount"]])}>
            <TextStyle size=#large>
              {formattedTotalAmountExcludingGlobalDiscounts->React.string}
            </TextStyle>
          </DivX>
        </DivX>
        <DivX style={StyleX.props([styles["row"]])}>
          <DivX style={StyleX.props([styles["label"]])}>
            <TextStyle size=#large>
              {template(
                t("Discount {{amount}}"),
                ~values={
                  "amount": switch discount.kind {
                  | Percent =>
                    (discount.value /. 100.)->Intl.percentFormat(~maximumFractionDigits=2)
                  | _ => discount.value->Intl.currencyFormat(~currency=#EUR)
                  },
                },
                (),
              )->React.string}
            </TextStyle>
          </DivX>
          <DivX style={StyleX.props([styles["amount"]])}>
            <TextStyle size=#large> {formattedDiscountAmount->React.string} </TextStyle>
          </DivX>
        </DivX>
      </Box>
    | _ => React.null
    }}
    <DivX style={StyleX.props([styles["row"]])}>
      <DivX style={StyleX.props([styles["label"]])}>
        <TextStyle weight={cart.taxesFree ? #strong : #regular} size=#large>
          {t("Total amount excl. VAT:")->React.string}
        </TextStyle>
      </DivX>
      <DivX style={StyleX.props([styles["amount"]])}>
        <TextStyle weight={cart.taxesFree ? #strong : #regular} size=#large>
          {formattedTotalAmountExcludingTaxes->React.string}
        </TextStyle>
      </DivX>
    </DivX>
    <DivX style={StyleX.props([styles["subrow"]])}>
      <Stack space=#xxsmall>
        <Inline>
          <DivX style={StyleX.props([styles["label"]])}>
            <TextStyle variation=#normal size=#xsmall>
              {t("Goods total excl. VAT")->React.string}
            </TextStyle>
          </DivX>
          <DivX style={StyleX.props([styles["amount"]])}>
            <TextStyle variation=#normal size=#xsmall>
              {formattedTotalAmountOfGoods->React.string}
            </TextStyle>
          </DivX>
        </Inline>
        {cart.fees
        ->Option.getExn
        ->Array.mapWithIndex((index, fee) => {
          let formattedFeeAmount = bigToFloat(fee.amount)->currencyFormat

          <Inline key={index->Int.toString}>
            <DivX style={StyleX.props([styles["label"]])}>
              <TextStyle variation=#normal size=#xsmall>
                {template(
                  t("Total {{feeKind}}"),
                  ~values={
                    "feeKind": switch fee.kind {
                    | Transport => t("Transport")
                    | Taxes => t("Taxes")
                    | Other => t("Other")
                    },
                  },
                  (),
                )->React.string}
              </TextStyle>
            </DivX>
            <DivX style={StyleX.props([styles["amount"]])}>
              <TextStyle variation=#normal size=#xsmall>
                {formattedFeeAmount->React.string}
              </TextStyle>
            </DivX>
          </Inline>
        })
        ->React.array}
        <Box spaceY=#xxsmall />
      </Stack>
    </DivX>
    {switch cart.taxes {
    | Some(taxes) =>
      taxes
      ->Array.keep(tax =>
        switch tax.amount {
        | Some(amount) => bigToFloat(amount) > 0.
        | None => false
        }
      )
      ->Array.map(tax => {
        let formattedTaxAmount = bigToFloat(tax.amount->Option.getExn)->currencyFormat

        <DivX
          key={formattedTaxAmount ++ tax.rate->Js.Float.toString}
          style={StyleX.props([styles["row"]])}>
          <DivX style={StyleX.props([styles["label"]])}>
            <TextStyle size=#large>
              {template(
                t("VAT {{amount}}"),
                ~values={"amount": tax.rate->Js.Float.toString->Js.String2.replace(".", ",")},
                (),
              )->React.string}
            </TextStyle>
          </DivX>
          <DivX style={StyleX.props([styles["amount"]])}>
            <TextStyle size=#large> {formattedTaxAmount->React.string} </TextStyle>
          </DivX>
        </DivX>
      })
      ->React.array
    | None => React.null
    }}
    {if cart.taxesFree {
      <DivX style={StyleX.props([style(~paddingInline="28px", ())])}>
        <TextStyle variation=#normal size=#large>
          {template(
            t("{{amount}} of deductible VAT (0.00%)"),
            ~values={
              "amount": currencyFormat(0.00),
            },
            (),
          )->React.string}
        </TextStyle>
      </DivX>
    } else {
      <DivX style={StyleX.props([styles["row"]])}>
        <DivX style={StyleX.props([styles["label"]])}>
          <TextStyle weight=#strong size=#large>
            {t("Total amount incl. VAT:")->React.string}
          </TextStyle>
        </DivX>
        <DivX style={StyleX.props([styles["amount"]])}>
          <TextStyle weight=#strong size=#large>
            {formattedTotalAmountIncludingTaxes->React.string}
          </TextStyle>
        </DivX>
      </DivX>
    }}
  </DivX>
}

// let make = React.memoCustomCompareProps(make, (oldProps, newProps) =>
//   oldProps["cart"] === newProps["cart"]
// )

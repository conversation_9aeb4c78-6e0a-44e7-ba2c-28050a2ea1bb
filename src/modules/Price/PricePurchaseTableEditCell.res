open Intl

module VariantPurchasePriceMutation = %graphql(`
  mutation updateVariant_purchasePrice($id: ID!, $input: InputUpdateVariant!) {
    updateVariant(id: $id, input: $input) {
      id
      purchasedPrice
    }
  }
`)

@react.component
let make = (
  ~value: float,
  ~minRetailPrice: option<float>=?,
  ~bulkUnit: option<string>,
  ~variantId: string,
  ~autoFocused=false,
  ~onChange=?,
  ~onFocus=?,
  ~onBlur=?,
) => {
  let (mutate, mutation) = VariantPurchasePriceMutation.use()
  let (optimisticValue, setOptimisticValue) = React.useState(() => None)
  let debouncedValue = ReactUpdateDebounced.use(
    optimisticValue,
    ~delay=ApolloConfig.mutationDebounceInterval,
  )

  let onMutatePurchasePriceRequested = React.useCallback0(purchasePrice => {
    let input = VariantPurchasePriceMutation.makeInputObjectInputUpdateVariant(
      ~purchasedPrice=purchasePrice,
      (),
    )
    mutate(VariantPurchasePriceMutation.makeVariables(~id=variantId, ~input, ()))
    ->FuturePromise.fromPromise
    ->Future.get(response =>
      switch response {
      | Ok(Ok({error: None})) => ()
      | _ => setOptimisticValue(_ => None)
      }
    )
  })

  // Update purchase price on debounced value change
  React.useEffect1(() => {
    if !mutation.loading {
      switch (debouncedValue, optimisticValue) {
      | (Some(debouncedValue), Some(optimisticValue)) if value !== optimisticValue =>
        onMutatePurchasePriceRequested(debouncedValue)
      | _ => ()
      }
    }
    None
  }, [debouncedValue])

  let onChange = React.useCallback0((value: float) => {
    switch onChange {
    | Some(onChange) => onChange(value)
    | _ => ()
    }
    setOptimisticValue(_ => Some(value))
  })

  // Remaps onBlur to force a mutation on input onBlur
  let onBlur = onBlur->Option.map((onBlur, ()) => {
    switch optimisticValue {
    | Some(optimisticValue) if value !== optimisticValue =>
      onMutatePurchasePriceRequested(optimisticValue)
    | _ => ()
    }
    onBlur()
  })

  let errorMessage = switch minRetailPrice {
  | Some(minRetailPrice) if value > minRetailPrice && minRetailPrice !== 0. =>
    Some(t("The purchase price shouldn't exceed the entered retail prices."))
  | _ => None
  }

  <InputNumberField
    ?errorMessage
    appender={Custom(
      Intl.toCurrencySymbol(#EUR) ++ bulkUnit->Option.mapWithDefault("", unit => ` / ${unit}`),
    )}
    precision=3
    minValue=0.
    shrinkInput=true
    autoFocused
    value={optimisticValue->Option.getWithDefault(value)}
    onChange
    ?onFocus
    ?onBlur
  />
}

// let make = React.memoCustomCompareProps(make, (oldProps, newProps) =>
//   oldProps["value"] === newProps["value"] &&
//   oldProps["minRetailPrice"] === newProps["minRetailPrice"] &&
//   oldProps["bulkUnit"] === newProps["bulkUnit"] &&
//   oldProps["variantId"] === newProps["variantId"]
// )

import React from 'react'

import { StoryFn, Meta } from '@storybook/react'

import { navigationToRoute } from '../../../src/primitives/Navigation'
import {
  Card,
  CardProps,
  cardVariationCommon,
  cardActionCallback,
  cardActionOpenLinkNewTab,
} from '../../../src/resources/layout-and-structure/Card'
import { TextStyle } from '../../../src/resources/typography/TextStyle'

const Template: StoryFn<CardProps> = (props: Omit<CardProps, 'children'>) => (
  <Card
    title="Title card"
    children={
      <div>
        <TextStyle>This is some content.</TextStyle>
      </div>
    }
    {...props}
  />
)

export default {
  title: 'Resources/Layout and structure/Card',
  component: Card,
} as Meta<typeof Card>

export const Default = Template.bind({})
Default.storyName = 'Default'
Default.args = {
  title: 'Title card',
  children: (
    <div>
      <TextStyle>This is some content.</TextStyle>
    </div>
  ),
  action: undefined,
  variation: cardVariationCommon,
  shadowed: false,
  grow: false,
}

export const WithTableVariation = Template.bind({})
WithTableVariation.storyName = 'With table variation'
WithTableVariation.args = { variation: 'table' }

export const WithRedirectionIconButton = Template.bind({})
WithRedirectionIconButton.storyName = 'With a redirection icon button'
WithRedirectionIconButton.args = {
  action: cardActionOpenLinkNewTab({
    icon: 'queue_arrow_right_light',
    title: 'Redirection title',
    to: navigationToRoute('/'),
  }),
}

export const WithRequestActionIconButton = Template.bind({})
WithRequestActionIconButton.storyName = 'With a request action icon button'
WithRequestActionIconButton.args = {
  action: cardActionCallback({
    icon: 'edit_light',
    title: 'Action title',
    callback: alert,
  }),
}

export const WithNoTitle = Template.bind({})
WithNoTitle.storyName = 'With no title'
WithNoTitle.args = { title: undefined }

export const WithShadowedSetToTrue = Template.bind({})
WithShadowedSetToTrue.storyName = 'With shadowed set to true'
WithShadowedSetToTrue.args = { shadowed: true }

export const WithLongTitle = Template.bind({})
WithLongTitle.storyName = 'With a long title'
WithLongTitle.args = {
  title:
    "I'm a long title and I'm responsive. I'm a long title and I'm responsive. I'm a long title and I'm responsive. I'm a long title and I'm responsive. I'm a long title and I'm responsive. I'm a long title and I'm responsive. ",
}

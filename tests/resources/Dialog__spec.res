open Vitest
open TestingLibraryReact

module TestableDialog = {
  @react.component
  let make = (
    ~children,
    ~header,
    ~commitDisabled=?,
    ~commitLoading=?,
    ~commitButtonText=?,
    ~onRequestCommit,
    ~onRequestClose,
  ) => {
    let {ref: popoverTriggerRef, state: popover, ariaProps: popoverAriaProps} = Popover.useTrigger(
      ~defaultOpened=true,
      (),
    )

    <Popover triggerRef=popoverTriggerRef state=popover>
      <Dialog
        header
        ?commitDisabled
        ?commitLoading
        ?commitButtonText
        ariaProps=popoverAriaProps.overlayProps
        onRequestCommit
        onRequestClose={() => {
          onRequestClose()
          popover.onRequestClose()
        }}>
        {children}
      </Dialog>
    </Popover>
  }
}

let userEvent = TestingLibraryEvent.setup()

itPromise("should render a dialog and interacts", async () => {
  let onRequestCommit = fn1(ignore)
  let onRequestClose = fn1(ignore)

  let {unmount} = render(
    <TestableDialog
      header={Dialog.Title({text: "Dialog title"})}
      onRequestCommit={onRequestCommit->fn}
      onRequestClose={onRequestClose->fn}>
      {"Some dialog content"->React.string}
    </TestableDialog>,
  )

  let dialog = screen->getByRoleWithOptionsExn(#dialog, {name: "Dialog title"})
  let discardButton = screen->getByRoleWithOptionsExn(#button, {name: "Discard"})
  let commitButton = screen->getByRoleWithOptionsExn(#button, {name: "Save"})

  expect(onRequestCommit)->toHaveBeenCalledTimes(0)
  expect(onRequestClose)->toHaveBeenCalledTimes(0)

  expect(dialog)->toBeVisible
  expect(dialog)->toHaveTextContent("Some dialog content")
  expect(discardButton)->toBeVisible
  expect(commitButton)->toBeVisible

  await userEvent->TestingLibraryEvent.click(discardButton)

  expect(onRequestClose)->toHaveBeenCalledTimes(1)

  await userEvent->TestingLibraryEvent.click(commitButton)

  expect(onRequestCommit)->toHaveBeenCalledTimes(1)

  mockClear(onRequestCommit)

  unmount()

  let {rerender} = render(
    <TestableDialog
      commitDisabled=true
      commitButtonText="Custom commit button"
      header={Dialog.Title({text: "Dialog title"})}
      onRequestCommit={onRequestCommit->fn}
      onRequestClose={onRequestClose->fn}>
      {"Some dialog content"->React.string}
    </TestableDialog>,
  )

  expect(screen->getByRoleWithOptionsExn(#button, {name: "Custom commit button"}))->toBeDefined

  expect(onRequestCommit)->toHaveBeenCalledTimes(0)

  await userEvent->TestingLibraryEvent.click(commitButton)

  expect(onRequestCommit)->toHaveBeenCalledTimes(0)

  rerender(
    <TestableDialog
      commitLoading=true
      commitButtonText="Custom commit button"
      header={Dialog.Title({text: "Dialog title"})}
      onRequestCommit={onRequestCommit->fn}
      onRequestClose={onRequestClose->fn}>
      {"Some dialog content"->React.string}
    </TestableDialog>,
  )->ignore

  expect(screen->queryByRoleWithOptions(#button, {name: "Custom commit button"}))->toBeNone

  let (_dismissButton, _discardButton, commitButton) = screen->getAllByRoleExn3(#button)

  expect(within(commitButton)->getByRoleWithOptionsExn(#status, {name: "Loading..."}))->toBeVisible
  expect(commitButton)->toHaveAttributeValue("aria-disabled", "true")
})

itPromise("should render a dialog with tabs and interacts", async () => {
  let onRequestCommit = fn1(ignore)
  let onRequestClose = fn1(ignore)
  let onTabChange = fn1(ignore)

  let {rerender} = render(
    <TestableDialog
      header={Dialog.Tabs({
        onChange: onTabChange->fn,
      })}
      onRequestCommit={onRequestCommit->fn}
      onRequestClose={onRequestClose->fn}>
      <Dialog.Tab key="first" title="First tab">
        {"Dialog first tab content"->React.string}
      </Dialog.Tab>
      <Dialog.Tab key="second" title="Second tab">
        {"Dialog second tab content"->React.string}
      </Dialog.Tab>
      <Dialog.Tab key="third" title="Third tab">
        {"Dialog third tab content"->React.string}
      </Dialog.Tab>
    </TestableDialog>,
  )

  let dialog = screen->getByRoleWithOptionsExn(#dialog, {name: "popover-dialog"})

  let tablist = screen->getByRoleExn(#tablist)
  let tabsLength = screen->getAllByRoleExn(#tab)->Array.length
  let (tab1, tab2, tab3) = screen->getAllByRoleExn3(#tab)

  mockClear(onTabChange)

  expect(dialog)->toBeVisible
  expect(onTabChange)->toHaveBeenCalledTimes(0)

  expect(tabsLength)->toBe(3)
  expect(tablist)->toBeVisible
  expect(tab1)->toBeVisible
  expect(tab2)->toBeVisible
  expect(tab3)->toBeVisible

  expect(tab1)->toHaveTextContent("First tab")
  expect(tab2)->toHaveTextContent("Second tab")
  expect(tab3)->toHaveTextContent("Third tab")

  expect(tab1)->toHaveAttributeValue("aria-selected", "true")
  expect(tab2)->toHaveAttributeValue("aria-selected", "false")
  expect(tab3)->toHaveAttributeValue("aria-selected", "false")

  expect(dialog)->toHaveTextContent("Dialog first tab content")
  expect(dialog)->Vitest.not->toHaveTextContent("Dialog second tab content")
  expect(dialog)->Vitest.not->toHaveTextContent("Dialog third tab content")

  await userEvent->TestingLibraryEvent.click(tab2)

  expect(onTabChange)->toHaveBeenCalledTimes(1)
  expect(onTabChange)->toHaveBeenLastCalledWith1("second")

  expect(tab1)->toHaveAttributeValue("aria-selected", "false")
  expect(tab2)->toHaveAttributeValue("aria-selected", "true")
  expect(tab3)->toHaveAttributeValue("aria-selected", "false")

  expect(dialog)->Vitest.not->toHaveTextContent("Dialog first tab content")
  expect(dialog)->toHaveTextContent("Dialog second tab content")
  expect(dialog)->Vitest.not->toHaveTextContent("Dialog third tab content")

  await userEvent->TestingLibraryEvent.keyboard("{arrowright}")

  expect(onTabChange)->toHaveBeenCalledTimes(2)
  expect(onTabChange)->toHaveBeenLastCalledWith1("third")

  expect(tab1)->toHaveAttributeValue("aria-selected", "false")
  expect(tab2)->toHaveAttributeValue("aria-selected", "false")
  expect(tab3)->toHaveAttributeValue("aria-selected", "true")

  expect(dialog)->Vitest.not->toHaveTextContent("Dialog first tab content")
  expect(dialog)->Vitest.not->toHaveTextContent("Dialog second tab content")
  expect(dialog)->toHaveTextContent("Dialog third tab content")

  mockClear(onTabChange)

  let disabledKeys = ["second"]

  rerender(
    <TestableDialog
      header={Dialog.Tabs({
        onChange: onTabChange->fn,
        disabledKeys,
      })}
      onRequestCommit={onRequestCommit->fn}
      onRequestClose={onRequestClose->fn}>
      <Dialog.Tab key="first" title="First tab">
        {"Dialog first tab content"->React.string}
      </Dialog.Tab>
      <Dialog.Tab key="second" title="Second tab">
        {"Dialog second tab content"->React.string}
      </Dialog.Tab>
      <Dialog.Tab key="third" title="Third tab">
        {"Dialog third tab content"->React.string}
      </Dialog.Tab>
    </TestableDialog>,
  )->ignore

  expect(tab1)->toHaveAttributeValue("aria-selected", "false")
  expect(tab2)->toHaveAttributeValue("aria-selected", "false")
  expect(tab3)->toHaveAttributeValue("aria-selected", "true")

  await userEvent->TestingLibraryEvent.click(tab2)

  expect(onTabChange)->toHaveBeenCalledTimes(0)
  expect(tab2)->toHaveAttributeValue("aria-disabled", "true")

  expect(tab1)->toHaveAttributeValue("aria-selected", "false")
  expect(tab2)->toHaveAttributeValue("aria-selected", "false")
  expect(tab3)->toHaveAttributeValue("aria-selected", "true")

  await userEvent->TestingLibraryEvent.keyboard("{arrowleft}")

  expect(tab1)->toHaveAttributeValue("aria-selected", "true")
  expect(tab2)->toHaveAttributeValue("aria-selected", "false")
  expect(tab3)->toHaveAttributeValue("aria-selected", "false")

  expect(onTabChange)->toHaveBeenCalledTimes(1)
  expect(onTabChange)->toHaveBeenLastCalledWith1("first")
})

itPromise("should render a dialog and interacts with keyboard", async () => {
  let onRequestCommit = fn1(ignore)
  let onRequestClose = fn1(ignore)

  let {unmount} = render(
    <TestableDialog
      header={Dialog.Title({text: "Dialog title"})}
      onRequestCommit={onRequestCommit->fn}
      onRequestClose={onRequestClose->fn}>
      {"Some dialog content"->React.string}
    </TestableDialog>,
  )

  let dialog = screen->getByRoleWithOptionsExn(#dialog, {name: "Dialog title"})

  dialog->WebAPI.HtmlElement.ofDomElement->Option.forEach(fn => fn->WebAPI.HtmlElement.focus)

  expect(onRequestCommit)->toHaveBeenCalledTimes(0)
  expect(onRequestClose)->toHaveBeenCalledTimes(0)

  await userEvent->TestingLibraryEvent.keyboard("{control>}{enter}")

  expect(onRequestCommit)->toHaveBeenCalledTimes(1)
  expect(onRequestClose)->toHaveBeenCalledTimes(0)

  await userEvent->TestingLibraryEvent.keyboard("{escape}")

  expect(onRequestCommit)->toHaveBeenCalledTimes(1)
  expect(onRequestClose)->toHaveBeenCalledTimes(1)

  mockClear(onRequestCommit)

  expect(onRequestCommit)->toHaveBeenCalledTimes(0)

  unmount()

  render(
    <TestableDialog
      commitDisabled=true
      header={Dialog.Title({text: "Dialog title"})}
      onRequestCommit={onRequestCommit->fn}
      onRequestClose={onRequestClose->fn}>
      {"Some dialog content"->React.string}
    </TestableDialog>,
  )->ignore

  await userEvent->TestingLibraryEvent.keyboard("{control>}{enter}")

  expect(onRequestCommit)->toHaveBeenCalledTimes(0)
})

itPromise("should render a dialog with a tab showing a tooltip when hovered", async () => {
  let onRequestCommit = fn1(ignore)
  let onRequestClose = fn1(ignore)

  render(
    <TestableDialog
      header={Dialog.Tabs({})}
      onRequestCommit={onRequestCommit->fn}
      onRequestClose={onRequestClose->fn}>
      <Dialog.Tab title="A Tab" tooltipText="A Tab tooltip">
        {"Dialog tab content"->React.string}
      </Dialog.Tab>
    </TestableDialog>,
  )->ignore

  let dialog = screen->getByRoleWithOptionsExn(#dialog, {name: "popover-dialog"})

  let tablist = screen->getByRoleExn(#tablist)
  let tab = screen->getByRoleExn(#tab)

  expect(dialog)->toBeVisible
  expect(tablist)->toBeVisible
  expect(tab)->toBeVisible

  expect(tab)->toHaveTextContent("A Tab")
  expect(dialog)->toHaveTextContent("Dialog tab content")

  expect(screen->queryByRoleWithOptions(#tooltip, {hidden: true}))->toBeNone

  await userEvent->TestingLibraryEvent.hover(tablist)
  await userEvent->TestingLibraryEvent.hover(tab)

  let tooltip = screen->getByRoleWithOptionsExn(#tooltip, {hidden: true})

  expect(tooltip)->toBeVisible
  expect(tooltip)->toHaveTextContent("A Tab tooltip")

  await userEvent->TestingLibraryEvent.unhover(tab)
  await userEvent->TestingLibraryEvent.unhover(tablist)

  await waitFor(() => expect(screen->queryByRoleWithOptions(#tooltip, {hidden: true}))->toBeNone)

  expect(tooltip)->Vitest.not->toBeVisible
})

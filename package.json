{"name": "app", "version": "0.0.0", "private": true, "author": "Wino Technologies", "type": "module", "engines": {"node": ">=20"}, "scripts": {"deps": "yarn --check-files --frozen-lockfile", "postinstall": "patch-package && yarn clean", "up": "yarn upgrade-interactive --latest && yarn postinstall", "build": "yarn deps && rescript format -check -all && rescript build -with-deps && tsc -b tsconfig.json && vite build", "storybook": "storybook build", "test": "vitest run", "format": "rescript format -all", "dev:test": "DEBUG_PRINT_LIMIT=100000 vitest watch --ui --api.host --api.port 5000", "dev:server": "vite --host", "dev:storybook": "storybook dev -p 7007", "dev:res": "rescript build -w", "dev:ts": "tsc -b tsconfig.json -w", "graphql:introspection": "npx get-graphql-schema https://xx.wino.fr/graphql -j > graphql_schema_gateway.json -h 'Authorization=Bearer xxx'", "clean": "find ./src -name '*.bs.js' -type f -delete && find ./tests -name '*.bs.js' -type f -delete && rescript clean"}, "dependencies": {"@apollo/client": "3.4.17", "@internationalized/date": "3.5.3", "@reasonml-community/graphql-ppx": "1.2.3", "@rescript/react": "0.11.0", "@sentry/react": "7.86.0", "@sentry/tracing": "7.86.0", "@stylexjs/stylex": "0.13.1", "@wino/accounting": "14.2.4", "@wino/translator": "latest", "exceljs": "4.3.0", "graphql": "15.5.3", "history": "4.10.1", "intl": "1.2.5", "jwt-decode": "3.1.2", "lenses-ppx": "6.1.10", "libphonenumber-js": "1.10.60", "logrocket": "3.0.1", "logrocket-react": "5.0.1", "moment": "2.29.4", "papaparse": "5.4.1", "posthog-js": "1.161.5", "qs": "6.12.1", "react": "18.3.1", "react-aria": "3.41.1", "react-dates": "21.8.0", "react-dom": "18.3.1", "react-router": "5.2.1", "react-router-dom": "5.3.0", "react-stately": "3.39.0", "rescript": "10.1.1", "rescript-apollo-client": "2.4.1", "rescript-future": "2.1.0", "uuid": "9.0.1", "vite-plugin-stylex": "0.13.0"}, "devDependencies": {"@storybook/addon-actions": "8.3.2", "@storybook/addon-docs": "8.3.2", "@storybook/addon-storysource": "8.3.2", "@storybook/components": "8.3.2", "@storybook/core-events": "8.3.2", "@storybook/react": "8.3.2", "@storybook/react-vite": "8.3.2", "@testing-library/jest-dom": "5.16.5", "@testing-library/react": "12.1.5", "@testing-library/react-hooks": "8.0.1", "@testing-library/user-event": "14.5.2", "@types/react": "17.0.14", "@types/react-dom": "17.0.14", "@types/react-router-dom": "5.3.3", "@vitejs/plugin-react": "4.2.1", "@vitest/coverage-v8": "1.6.0", "@vitest/ui": "1.6.0", "blob-polyfill": "7.0.20220408", "cross-fetch": "4.0.0", "jsdom": "24.0.0", "msw": "1.3.2", "patch-package": "8.0.0", "storybook": "8.3.2", "typescript": "5.4.5", "vite": "5.2.14", "vitest": "1.6.0"}}